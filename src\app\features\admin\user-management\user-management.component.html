<div class="header-section">
  <div class="container-fluid">
    <div class="row align-items-center">
      <div class="col-6 col-md-3 col-lg-2">
        <button class="btn btn-outline-secondary" (click)="goBack()">
          <i class="fas fa-arrow-left me-2"></i>
        </button>
      </div>
      <div class="col-12 col-md-6 col-lg-8 order-first order-md-0">
        <div class="page-title text-center">
          <h1 class="mb-0">
            <i class="fas fa-users me-2 text-primary d-none d-sm-inline"></i>
            <span class="d-none d-md-inline">User Management</span>
            <span class="d-md-none">Users</span>
          </h1>
        </div>
      </div>
      <div class="col-6 col-md-3 col-lg-2">
        <button class="btn btn-success add-btn w-100" (click)="showAddUserForm()" *ngIf="!showAddForm">
          <i class="fas fa-user-plus me-1"></i>
          <span class="d-none d-sm-inline">Add</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Success/Error Messages -->
<div class="alerts-container">
  <div class="alert alert-success alert-dismissible fade show" *ngIf="success" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    {{ success }}
    <button type="button" class="btn-close" (click)="success = ''" aria-label="Close"></button>
  </div>

  <div class="alert alert-danger alert-dismissible fade show" *ngIf="error" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="error = ''" aria-label="Close"></button>
  </div>
</div>

<!-- Loading Component -->
<app-loading *ngIf="loading && !showAddForm" message="Loading users..."></app-loading>

<!-- Add/Edit User Form -->
<div class="form-card mb-4" *ngIf="showAddForm">
  <div class="card shadow-sm border-0">
    <div class="card-header bg-light border-0">
      <div class="d-flex align-items-center">
        <i class="fas {{ editingUser ? 'fa-user-edit' : 'fa-user-plus' }} me-2 text-primary"></i>
        <h5 class="mb-0 fw-semibold">{{ editingUser ? 'Edit User' : 'Add New User' }}</h5>
      </div>
    </div>
    <div class="card-body p-4">
      <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
        <div class="row">
          <!-- First Name -->
          <div class="col-md-6 mb-4">
            <label for="firstName" class="form-label">
              <i class="fas fa-user me-2 text-primary"></i>First Name *
            </label>
            <input type="text" class="form-control form-control-lg" id="firstName" formControlName="firstName"
              placeholder="Enter first name" [class.is-invalid]="f['firstName'].invalid && f['firstName'].touched"
              maxlength="50">
            <div class="form-text text-muted mb-2">
              <small><i class="fas fa-info-circle me-1"></i>Enter the user's first name</small>
            </div>
            <div class="invalid-feedback" *ngIf="f['firstName'].invalid && f['firstName'].touched">
              <div *ngIf="f['firstName'].errors?.['required']">
                <i class="fas fa-exclamation-circle me-1"></i>First name is required
              </div>
              <div *ngIf="f['firstName'].errors?.['minlength']">
                <i class="fas fa-exclamation-circle me-1"></i>First name must be at least 2 characters
              </div>
              <div *ngIf="f['firstName'].errors?.['maxlength']">
                <i class="fas fa-exclamation-circle me-1"></i>First name cannot exceed 50 characters
              </div>
            </div>
          </div>

          <!-- Last Name -->
          <div class="col-md-6 mb-4">
            <label for="lastName" class="form-label">
              <i class="fas fa-user me-2 text-primary"></i>Last Name *
            </label>
            <input type="text" class="form-control form-control-lg" id="lastName" formControlName="lastName"
              placeholder="Enter last name" [class.is-invalid]="f['lastName'].invalid && f['lastName'].touched"
              maxlength="50">
            <div class="form-text text-muted mb-2">
              <small><i class="fas fa-info-circle me-1"></i>Enter the user's last name</small>
            </div>
            <div class="invalid-feedback" *ngIf="f['lastName'].invalid && f['lastName'].touched">
              <div *ngIf="f['lastName'].errors?.['required']">
                <i class="fas fa-exclamation-circle me-1"></i>Last name is required
              </div>
              <div *ngIf="f['lastName'].errors?.['minlength']">
                <i class="fas fa-exclamation-circle me-1"></i>Last name must be at least 2 characters
              </div>
              <div *ngIf="f['lastName'].errors?.['maxlength']">
                <i class="fas fa-exclamation-circle me-1"></i>Last name cannot exceed 50 characters
              </div>
            </div>
          </div>

          <!-- Email -->
          <div class="col-md-6 mb-4">
            <label for="email" class="form-label">
              <i class="fas fa-envelope me-2 text-primary"></i>Email Address *
            </label>
            <input type="email" class="form-control form-control-lg" id="email" formControlName="email"
              placeholder="<EMAIL>" [class.is-invalid]="f['email'].invalid && f['email'].touched"
              maxlength="100">
            <div class="form-text text-muted mb-2">
              <small><i class="fas fa-info-circle me-1"></i>Enter a valid email address</small>
            </div>
            <div class="invalid-feedback" *ngIf="f['email'].invalid && f['email'].touched">
              <div *ngIf="f['email'].errors?.['required']">
                <i class="fas fa-exclamation-circle me-1"></i>Email is required
              </div>
              <div *ngIf="f['email'].errors?.['email']">
                <i class="fas fa-exclamation-circle me-1"></i>Please enter a valid email address
              </div>
              <div *ngIf="f['email'].errors?.['maxlength']">
                <i class="fas fa-exclamation-circle me-1"></i>Email cannot exceed 100 characters
              </div>
            </div>
          </div>

          <!-- Role -->
          <div class="col-md-6 mb-4">
            <label for="role" class="form-label">
              <i class="fas fa-user-tag me-2 text-primary"></i>Role *
            </label>
            <!-- <select class="form-select form-control-lg" id="role" formControlName="role"
              [class.is-invalid]="f['role'].invalid && f['role'].touched">
              <option value="">Select a role</option>
              <option *ngFor="let role of roles" [value]="role.name">{{ role.name }}</option>
            </select> -->

            <ng-select [items]="roles" bindLabel="name" bindValue="name" [multiple]="false" [closeOnSelect]="true"
              [searchable]="true" [clearable]="true" placeholder="Select a role" formControlName="role"
              [class.is-invalid]="f['role'].invalid && f['role'].touched" class="custom" (change)="onRoleChange($event)">

              <!-- Custom option template -->
              <ng-option *ngFor="let role of roles" [value]="role.name">
                <i class="fas fa-user-tag me-2"></i>{{ role.name }}
              </ng-option>

              <!-- Custom loading template -->
              <ng-template ng-loading-tmp>
                <i class="fas fa-spinner fa-spin me-2"></i>Loading roles...
              </ng-template>

              <!-- Custom not found template -->
              <ng-template ng-notfound-tmp>
                <div class="text-center p-2">
                  <i class="fas fa-search me-2"></i>No roles found
                </div>
              </ng-template>
            </ng-select>
            <div class="form-text text-muted mb-2">
              <small><i class="fas fa-info-circle me-1"></i>Select the user's role in the organization</small>
            </div>
            <div class="invalid-feedback" *ngIf="f['role'].invalid && f['role'].touched">
              <div *ngIf="f['role'].errors?.['required']">
                <i class="fas fa-exclamation-circle me-1"></i>Role is required
              </div>
            </div>
          </div>

          <!-- Department -->
          <div class="col-md-6 mb-4">
            <label for="departmentId" class="form-label">
              <i class="fas fa-building me-2 text-primary"></i>Department *
            </label>
            <ng-select [items]="departments" bindLabel="name" bindValue="id" [multiple]="isMultipleDepartmentsAllowed()" [closeOnSelect]="!isMultipleDepartmentsAllowed()"
              [searchable]="true" [clearable]="true" [placeholder]="getDepartmentPlaceholder()" formControlName="departmentId"
              [class.is-invalid]="f['departmentId'].invalid && f['departmentId'].touched" class="custom">

              <!-- Custom option template -->
              <ng-option *ngFor="let department of departments" [value]="department.id">
                <i class="fas fa-building me-2"></i>{{ department.name }}
              </ng-option>

              <!-- Custom loading template -->
              <ng-template ng-loading-tmp>
                <i class="fas fa-spinner fa-spin me-2"></i>Loading departments...
              </ng-template>

              <!-- Custom not found template -->
              <ng-template ng-notfound-tmp>
                <div class="text-center p-2">
                  <i class="fas fa-search me-2"></i>No departments found
                </div>
              </ng-template>
            </ng-select>
            <div class="form-text text-muted mb-2" *ngIf="isMultipleDepartmentsAllowed()">
              <small><i class="fas fa-info-circle me-1"></i>CompanyAdministrator can be assigned to multiple departments</small>
            </div>
            <div class="form-text text-muted mb-2" *ngIf="!isMultipleDepartmentsAllowed()">
              <small><i class="fas fa-info-circle me-1"></i>Select the user's department</small>
            </div>
            <div class="invalid-feedback" *ngIf="f['departmentId'].invalid && f['departmentId'].touched">
              <div *ngIf="f['departmentId'].errors?.['required']">
                <i class="fas fa-exclamation-circle me-1"></i>Department is required
              </div>
            </div>
          </div>

          <!-- Supervisor -->
          <!-- <div class="col-md-6 mb-4">
            <label for="supervisorId" class="form-label">
              <i class="fas fa-user-tie me-2 text-primary"></i>Supervisor
            </label>
            <select 
              class="form-select form-control-lg" 
              id="supervisorId"
              formControlName="supervisorId">
              <option value="">Select a supervisor (optional)</option>
              <option *ngFor="let supervisor of supervisors" [value]="supervisor.id">
                {{ supervisor.firstName }} {{ supervisor.lastName }} ({{ supervisor.role }})
              </option>
            </select>
            <div class="form-text text-muted mb-2">
              <small><i class="fas fa-info-circle me-1"></i>Optionally assign a supervisor</small>
            </div>
          </div> -->

          <!-- Active Status (only for edit) -->
          <div class="col-md-6 mb-4" *ngIf="editingUser">
            <label class="form-label">
              <i class="fas fa-toggle-on me-2 text-primary"></i>Status
            </label>
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="isActive" formControlName="isActive">
              <label class="form-check-label" for="isActive">
                User is active
              </label>
            </div>
            <div class="form-text text-muted">
              <small><i class="fas fa-info-circle me-1"></i>Inactive users cannot log in</small>
            </div>
          </div>
        </div>

        <div class="form-actions d-flex gap-2 flex-wrap">
          <button type="submit" class="btn btn-primary px-4" [disabled]="userForm.invalid || loading">
            <span *ngIf="loading" class="spinner-border spinner-border-sm me-2" role="status"></span>
            <i class="fas {{ editingUser ? 'fa-save' : 'fa-user-plus' }} me-2"></i>
            {{ editingUser ? 'Update User' : 'Create User' }}
          </button>
          <button type="button" class="btn btn-outline-secondary px-4" (click)="cancelForm()">
            <i class="fas fa-times me-2"></i>
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Users List -->
<div class="users-content" *ngIf="!loading && !showAddForm">
  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
      <div class="stats-card card text-center border-0 shadow-sm h-100">
        <div class="card-body py-4">
          <div class="stats-icon mb-3">
            <i class="fas fa-users fa-2x text-primary"></i>
          </div>
          <h5 class="card-title text-muted mb-2">Total Users</h5>
          <h2 class="text-primary fw-bold mb-0">{{ users.length }}</h2>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
      <div class="stats-card card text-center border-0 shadow-sm h-100">
        <div class="card-body py-4">
          <div class="stats-icon mb-3">
            <i class="fas fa-user-check fa-2x text-success"></i>
          </div>
          <h5 class="card-title text-muted mb-2">Active Users</h5>
          <h2 class="text-success fw-bold mb-0">{{ getActiveUsersCount() }}</h2>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
      <div class="stats-card card text-center border-0 shadow-sm h-100">
        <div class="card-body py-4">
          <div class="stats-icon mb-3">
            <i class="fas fa-user-times fa-2x text-warning"></i>
          </div>
          <h5 class="card-title text-muted mb-2">Inactive Users</h5>
          <h2 class="text-warning fw-bold mb-0">{{ getInactiveUsersCount() }}</h2>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
      <div class="stats-card card text-center border-0 shadow-sm h-100">
        <div class="card-body py-4">
          <div class="stats-icon mb-3">
            <i class="fas fa-building fa-2x text-info"></i>
          </div>
          <h5 class="card-title text-muted mb-2">Departments</h5>
          <h2 class="text-info fw-bold mb-0">{{ departments.length }}</h2>
        </div>
      </div>
    </div>
  </div>

  <!-- Users Table -->
  <div class="users-table-card card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
      <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
          <i class="fas fa-list me-2 text-primary"></i>
          <h5 class="mb-0 fw-semibold">All Users</h5>
        </div>
        <div class="table-actions d-none d-md-block">
          <small class="text-muted">{{ users.length }} user{{ getUsersPlural() }} found</small>
        </div>
      </div>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
            <tr>
              <th class="border-0 fw-semibold text-dark">
                <i class="fas fa-user me-1 d-none d-sm-inline"></i>
                <span class="d-none d-sm-inline">Name</span>
                <span class="d-sm-none">User</span>
              </th>
              <th class="border-0 fw-semibold text-dark d-none d-lg-table-cell">
                <i class="fas fa-envelope me-1"></i>Email
              </th>
              <th class="border-0 fw-semibold text-dark">
                <i class="fas fa-user-tag me-1 d-none d-sm-inline"></i>
                <span class="d-none d-sm-inline">Role</span>
                <span class="d-sm-none">R</span>
              </th>
              <th class="border-0 fw-semibold text-dark d-none d-xl-table-cell">
                <i class="fas fa-building me-1"></i>Department
              </th>
              <!-- <th class="border-0 fw-semibold text-dark d-none d-xl-table-cell">
                <i class="fas fa-user-tie me-1"></i>Supervisor
              </th> -->
              <th class="border-0 fw-semibold text-dark text-center d-none d-md-table-cell">
                <i class="fas fa-toggle-on me-1"></i>
                <span class="d-none d-lg-inline">Status</span>
                <span class="d-lg-none">S</span>
              </th>
              <th class="border-0 fw-semibold text-dark text-center">
                <i class="fas fa-cogs me-1 d-none d-sm-inline"></i>
                <span class="d-none d-sm-inline">Actions</span>
                <span class="d-sm-none">Act</span>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of users; let i = index" class="user-row">
              <td class="align-middle">
                <div class="user-info">
                  <strong class="text-dark d-block">{{ user.firstName }} {{ user.lastName }}</strong>
                  <small class="text-muted d-lg-none">{{ user.email }}</small>
                  <div class="d-sm-none mt-1">
                    <span class="badge bg-primary me-1">{{ getUserRoleName(user) }}</span>
                    <span class="badge" [class]="getUserStatusBadgeClass(user)">
                      {{ getUserStatusText(user) }}
                    </span>
                  </div>
                </div>
              </td>
              <td class="align-middle d-none d-lg-table-cell">
                <div class="user-email">
                  <span class="text-muted">{{ user.email }}</span>
                </div>
              </td>
              <td class="align-middle d-none d-sm-table-cell">
                <span class="badge bg-primary">{{ getUserRoleName(user) }}</span>
                <div class="d-md-none mt-1">
                  <small class="text-muted">{{ getUserDepartmentName(user) }}</small>
                </div>
              </td>
              <td class="align-middle d-none d-xl-table-cell">
                <span class="text-muted">{{ getUserDepartmentName(user) }}</span>
              </td>
              <!-- <td class="align-middle d-none d-xl-table-cell">
                <span class="text-muted">{{ getUserSupervisorName(user) }}</span>
              </td> -->
              <td class="align-middle text-center d-none d-md-table-cell">
                <span class="badge" [class]="getUserStatusBadgeClass(user)">
                  {{ getUserStatusText(user) }}
                </span>
              </td>
              <td class="align-middle text-center">
                <!-- <button type="button" class="btn btn-outline-primary btn-sm action-menu-btn"
                  (click)="openUserActionsModal(user)" title="User Actions">
                  <i class="fas fa-cog me-1"></i>
                  <span class="d-none d-lg-inline">Actions</span>
                </button> -->

                <div class="action-buttons">
                  <button type="button" class="btn btn-sm btn-outline-primary me-2" (click)="editUser(user)"
                    title="Edit Department">
                    <i class="fas fa-edit"></i>
                    <!-- <span class="d-none d-lg-inline ms-1">Edit</span> -->
                  </button>
                  <button type="button" class="btn btn-sm btn-outline-danger" (click)="deleteUser(user)"
                    title="Delete Department">
                    <i class="fas fa-trash-alt"></i>
                    <!-- <span class="d-none d-lg-inline ms-1">Delete</span> -->
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div *ngIf="isUsersEmpty()" class="empty-state text-center py-5">
          <div class="empty-icon mb-4">
            <i class="fas fa-users fa-4x text-muted opacity-50"></i>
          </div>
          <h4 class="text-muted mb-3">No users found</h4>
          <p class="text-muted mb-4">Create your first user to get started with user management.</p>
          <button class="btn btn-primary btn-lg" (click)="showAddUserForm()">
            <i class="fas fa-user-plus me-2"></i>
            Add First User
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- User Actions Modal -->
<div class="modal fade" id="userActionsModal" tabindex="-1" aria-labelledby="userActionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="userActionsModalLabel">
          <i class="fas fa-user-cog me-2 text-primary"></i>
          User Actions
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" *ngIf="selectedUser">
        <!-- User Info Header -->
        <div class="user-info-header mb-4 p-3 bg-light rounded">
          <div class="d-flex align-items-center">
            <div class="user-avatar me-3">
              <div class="avatar-circle">
                <i class="fas fa-user"></i>
              </div>
            </div>
            <div>
              <h6 class="mb-1 fw-bold">{{ selectedUser.firstName }} {{ selectedUser.lastName }}</h6>
              <small class="text-muted">{{ selectedUser.email }}</small>
              <div class="mt-1">
                <span class="badge bg-primary me-2">{{ getUserRoleName(selectedUser) }}</span>
                <span class="badge" [class]="getUserStatusBadgeClass(selectedUser)">
                  {{ getUserStatusText(selectedUser) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-grid">
          <button type="button" class="btn btn-outline-primary action-card"
            (click)="editUser(selectedUser); closeModal()">
            <div class="action-icon">
              <i class="fas fa-edit"></i>
            </div>
            <div class="action-content">
              <h6 class="mb-1">Edit User</h6>
              <small class="text-muted">Modify user information and settings</small>
            </div>
          </button>

          <button type="button" class="btn btn-outline-info action-card"
            (click)="resetPassword(selectedUser); closeModal()">
            <div class="action-icon">
              <i class="fas fa-sync-alt"></i>
            </div>
            <div class="action-content">
              <h6 class="mb-1">Reset Password</h6>
              <small class="text-muted">Send password reset email to user</small>
            </div>
          </button>

          <button type="button" class="btn action-card" [class]="getToggleButtonClass(selectedUser)"
            (click)="toggleUserStatus(selectedUser); closeModal()">
            <div class="action-icon">
              <i class="fas" [class]="getToggleButtonIcon(selectedUser)"></i>
            </div>
            <div class="action-content">
              <h6 class="mb-1">{{ getToggleButtonText(selectedUser) }} User</h6>
              <small class="text-muted">{{ getToggleButtonDescription(selectedUser) }}</small>
            </div>
          </button>

          <button type="button" class="btn btn-outline-danger action-card"
            (click)="deleteUser(selectedUser); closeModal()">
            <div class="action-icon">
              <i class="fas fa-trash-alt"></i>
            </div>
            <div class="action-content">
              <h6 class="mb-1">Delete User</h6>
              <small class="text-muted">Permanently remove user from system</small>
            </div>
          </button>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="fas fa-times me-1"></i>Close
        </button>
      </div>
    </div>
  </div>
</div>