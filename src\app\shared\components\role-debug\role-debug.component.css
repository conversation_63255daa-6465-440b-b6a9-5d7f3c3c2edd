.role-debug-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1050;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-radius: 0.5rem;
}

.card {
  border: none;
  border-radius: 0.5rem;
}

.card-header {
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.user-info p,
.permissions p {
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.badge {
  font-size: 0.8rem;
}

.btn-group-vertical .btn {
  text-align: left;
  font-size: 0.9rem;
}

.btn-group-vertical .btn:hover {
  transform: translateX(5px);
  transition: transform 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .role-debug-panel {
    position: relative;
    top: auto;
    right: auto;
    width: 100%;
    max-height: none;
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}

/* Color adjustments */
.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.text-warning {
  color: #ffc107 !important;
}

.text-info {
  color: #17a2b8 !important;
}

.text-primary {
  color: #007bff !important;
}
