<div class="container-fluid p-0">
  <div class="row m-0">
    <div class="col-12 p-0">    
      <div class="login-card">
        <div class="login-main"> 
          <div class="text-center">
            <a class="logo" href="#">VE</a>
          </div>
          
          <app-loading *ngIf="loading" message="Signing you in..."></app-loading>
          
          <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="theme-form" *ngIf="!loading">
            <h1 class="text-center">{{ 'auth.login' | translate }}</h1>
            
            <div class="alert alert-danger" *ngIf="error">
              {{ error }}
            </div>
            
            <div class="form-group">
              <label class="col-form-label">{{ 'auth.email' | translate }}</label>
              <input 
                class="form-control" 
                type="email" 
                formControlName="email"
                placeholder="<EMAIL>"
                [class.is-invalid]="f['email'].invalid && f['email'].touched">
              <div class="invalid-feedback" *ngIf="f['email'].invalid && f['email'].touched">
                <div *ngIf="f['email'].errors?.['required']">Email is required</div>
                <div *ngIf="f['email'].errors?.['email']">Please enter a valid email</div>
              </div>
            </div>
            
            <div class="form-group">
              <label class="col-form-label">{{ 'auth.password' | translate }}</label>
              <div class="form-input position-relative">
                <input 
                  class="form-control" 
                  type="password" 
                  formControlName="password"
                  placeholder="*********"
                  [class.is-invalid]="f['password'].invalid && f['password'].touched">
                <div class="invalid-feedback" *ngIf="f['password'].invalid && f['password'].touched">
                  <div *ngIf="f['password'].errors?.['required']">Password is required</div>
                  <div *ngIf="f['password'].errors?.['minlength']">Password must be at least 6 characters</div>
                </div>
              </div>
            </div>
            
            <div class="form-group mb-0">
              <div class="text-end">
                <button
                  class="btn btn-primary btn-block w-100 mt-3"
                  type="submit"
                  [disabled]="loginForm.invalid">
                  {{ 'auth.signInAsAdmin' | translate }}
                </button>
              </div>
            </div>
          </form>

          <!-- Employee Login Link -->
          <div class="text-center mt-4" *ngIf="!loading">
            <hr class="my-3">
            <p class="text-muted mb-2">Are you an employee?</p>
            <a
              routerLink="/employee-login"
              class="btn btn-outline-info btn-sm">
              <i class="fas fa-users me-2"></i>
              Employee Login
            </a>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
