<div class="header-section">
  <div class="d-flex justify-content-between align-items-center">
    <button class="btn btn-outline-secondary back-btn" (click)="goBack()">
      <i class="fas fa-arrow-left me-2"></i>
    </button>
    <div class="page-title">
      <h1 class="mb-0">
        <i class="fas fa-building me-2 text-primary"></i>
        Department Management
      </h1>
    </div>
    <button class="btn btn-success add-btn" (click)="showAddDepartmentForm()" *ngIf="!showAddForm">
      <i class="fas fa-plus me-2"></i>
      <span class="d-none d-sm-inline">Add</span>
      <span class="d-sm-none">Add</span>
    </button>
  </div>
</div>

<!-- Success/Error Messages -->
<div class="alerts-container">
  <div class="alert alert-success alert-dismissible fade show" *ngIf="success" role="alert">
    <i class="fas fa-check-circle me-2"></i>
    {{ success }}
    <button type="button" class="btn-close" (click)="success = ''" aria-label="Close"></button>
  </div>

  <div class="alert alert-danger alert-dismissible fade show" *ngIf="error" role="alert">
    <i class="fas fa-exclamation-triangle me-2"></i>
    {{ error }}
    <button type="button" class="btn-close" (click)="error = ''" aria-label="Close"></button>
  </div>
</div>

<!-- Loading Component -->
<app-loading *ngIf="loading && !showAddForm" message="Loading departments..."></app-loading>

<!-- Add/Edit Department Form -->
<div class="form-card mb-4" *ngIf="showAddForm">
  <div class="card shadow-sm border-0">
    <div class="card-header bg-light border-0">
      <div class="d-flex align-items-center">
        <i class="fas {{ editingDepartment ? 'fa-edit' : 'fa-plus-circle' }} me-2 text-primary"></i>
        <h5 class="mb-0 fw-semibold">{{ editingDepartment ? 'Edit Department' : 'Add New Department' }}</h5>
      </div>
    </div>
    <div class="card-body p-4">
    <form [formGroup]="departmentForm" (ngSubmit)="onSubmit()">
      <div class="row">
        <div class="col-md-6 mb-4">
          <label for="name" class="form-label">
            <i class="fas fa-building me-2 text-primary"></i>Department Name *
          </label>
          <input
            type="text"
            class="form-control form-control-lg"
            id="name"
            formControlName="name"
            placeholder="e.g., Engineering, Marketing, Human Resources"
            [class.is-invalid]="f['name'].invalid && f['name'].touched"
            maxlength="100">
          <div class="form-text text-muted mb-2">
            <small><i class="fas fa-info-circle me-1"></i>Choose a clear, descriptive name for the department</small>
          </div>
          <div class="invalid-feedback" *ngIf="f['name'].invalid && f['name'].touched">
            <div *ngIf="f['name'].errors?.['required']">
              <i class="fas fa-exclamation-circle me-1"></i>Department name is required
            </div>
            <div *ngIf="f['name'].errors?.['minlength']">
              <i class="fas fa-exclamation-circle me-1"></i>Department name must be at least 2 characters
            </div>
            <div *ngIf="f['name'].errors?.['maxlength']">
              <i class="fas fa-exclamation-circle me-1"></i>Department name cannot exceed 100 characters
            </div>
          </div>
        </div>

        <div class="col-md-6 mb-4">
          <label for="description" class="form-label">
            <i class="fas fa-align-left me-2 text-primary"></i>Description *
          </label>
          <textarea
            class="form-control form-control-lg"
            id="description"
            formControlName="description"
            rows="4"
            placeholder="Describe the department's role, responsibilities, and key functions..."
            [class.is-invalid]="f['description'].invalid && f['description'].touched"
            maxlength="500"></textarea>
          <div class="form-text text-muted mb-2">
            <small><i class="fas fa-info-circle me-1"></i>Provide a clear description of what this department does</small>
          </div>
          <div class="invalid-feedback" *ngIf="f['description'].invalid && f['description'].touched">
            <div *ngIf="f['description'].errors?.['required']">
              <i class="fas fa-exclamation-circle me-1"></i>Description is required
            </div>
            <div *ngIf="f['description'].errors?.['minlength']">
              <i class="fas fa-exclamation-circle me-1"></i>Description must be at least 5 characters
            </div>
            <div *ngIf="f['description'].errors?.['maxlength']">
              <i class="fas fa-exclamation-circle me-1"></i>Description cannot exceed 500 characters
            </div>
          </div>
        </div>
      </div>
      
      <div class="form-actions d-flex gap-2 flex-wrap">
        <button
          type="submit"
          class="btn btn-primary px-4"
          [disabled]="departmentForm.invalid || loading">
          <span *ngIf="loading" class="spinner-border spinner-border-sm me-2" role="status"></span>
          <i class="fas {{ editingDepartment ? 'fa-save' : 'fa-plus' }} me-2"></i>
          {{ editingDepartment ? 'Update Department' : 'Create Department' }}
        </button>
        <button
          type="button"
          class="btn btn-outline-secondary px-4"
          (click)="cancelForm()">
          <i class="fas fa-times me-2"></i>
          Cancel
        </button>
      </div>
    </form>
    </div>
  </div>
</div>

<!-- Departments List -->
<div class="departments-content" *ngIf="!loading && !showAddForm">
  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
      <div class="stats-card card text-center border-0 shadow-sm h-100">
        <div class="card-body py-4">
          <div class="stats-icon mb-3">
            <i class="fas fa-building fa-2x text-primary"></i>
          </div>
          <h5 class="card-title text-muted mb-2">Total Departments</h5>
          <h2 class="text-primary fw-bold mb-0">{{ departments.length }}</h2>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
      <div class="stats-card card text-center border-0 shadow-sm h-100">
        <div class="card-body py-4">
          <div class="stats-icon mb-3">
            <i class="fas fa-users fa-2x text-success"></i>
          </div>
          <h5 class="card-title text-muted mb-2">Active Departments</h5>
          <h2 class="text-success fw-bold mb-0">{{ departments.length }}</h2>
        </div>
      </div>
    </div>
  </div>

  <!-- Departments Table -->
  <div class="departments-table-card card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
      <div class="d-flex align-items-center justify-content-between">
        <div class="d-flex align-items-center">
          <i class="fas fa-list me-2 text-primary"></i>
          <h5 class="mb-0 fw-semibold">All Departments</h5>
        </div>
        <div class="table-actions d-none d-md-block">
          <small class="text-muted">{{ departments.length }} department{{ departments.length !== 1 ? 's' : '' }} found</small>
        </div>
      </div>
    </div>
    <div class="card-body p-0">
      <div class="table-responsive">
        <table class="table table-hover mb-0">
          <thead class="table-light">
            <tr>
              <th class="border-0 fw-semibold text-dark">
                <i class="fas fa-building me-2"></i>Name
              </th>
              <th class="border-0 fw-semibold text-dark">
                <i class="fas fa-info-circle me-2"></i>Description
              </th>
              <th class="border-0 fw-semibold text-dark d-none d-md-table-cell">
                <i class="fas fa-calendar me-2"></i>Created
              </th>
              <th class="border-0 fw-semibold text-dark text-center">
                <i class="fas fa-cogs me-2"></i>Actions
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let department of departments; let i = index" class="department-row">
              <td class="align-middle">
                <div class="department-name">
                  <strong class="text-dark">{{ department.name }}</strong>
                </div>
              </td>
              <td class="align-middle">
                <div class="department-description">
                  <span class="text-muted">{{ department.description }}</span>
                </div>
              </td>
              <td class="align-middle d-none d-md-table-cell">
                <small class="text-muted">
                  <i class="fas fa-clock me-1"></i>
                  {{ department.createdAt | date:'MMM dd, yyyy' }}
                </small>
              </td>
              <td class="align-middle text-center">
                <div class="action-buttons">
                  <button
                    type="button"
                    class="btn btn-sm btn-outline-primary me-2"
                    (click)="editDepartment(department)"
                    title="Edit Department">
                    <i class="fas fa-edit"></i>
                    <!-- <span class="d-none d-lg-inline ms-1">Edit</span> -->
                  </button>
                  <button
                    type="button"
                    class="btn btn-sm btn-outline-danger"
                    (click)="deleteDepartment(department)"
                    title="Delete Department">
                    <i class="fas fa-trash-alt"></i>
                    <!-- <span class="d-none d-lg-inline ms-1">Delete</span> -->
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div *ngIf="departments.length === 0" class="empty-state text-center py-5">
          <div class="empty-icon mb-4">
            <i class="fas fa-building fa-4x text-muted opacity-50"></i>
          </div>
          <h4 class="text-muted mb-3">No departments found</h4>
          <p class="text-muted mb-4">Create your first department to get started with organizing your company structure.</p>
          <button class="btn btn-primary btn-lg" (click)="showAddDepartmentForm()">
            <i class="fas fa-plus me-2"></i>
            Add First Department
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
