import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { DepartmentsComponent } from './departments.component';
import { DepartmentService, Department } from '../../../core/services/department.service';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

describe('DepartmentsComponent', () => {
  let component: DepartmentsComponent;
  let fixture: ComponentFixture<DepartmentsComponent>;
  let mockDepartmentService: jasmine.SpyObj<DepartmentService>;
  let mockRouter: jasmine.SpyObj<Router>;

  const mockDepartments: Department[] = [
    {
      id: 1,
      name: 'Engineering',
      description: 'Software development and engineering team',
      createdAt: '2024-01-01T00:00:00Z'
    },
    {
      id: 2,
      name: 'Marketing',
      description: 'Marketing and communications team',
      createdAt: '2024-01-02T00:00:00Z'
    }
  ];

  beforeEach(async () => {
    const departmentServiceSpy = jasmine.createSpyObj('DepartmentService', [
      'getAllDepartments',
      'createDepartment',
      'updateDepartment',
      'deleteDepartment'
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [DepartmentsComponent, ReactiveFormsModule, LoadingComponent],
      providers: [
        { provide: DepartmentService, useValue: departmentServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DepartmentsComponent);
    component = fixture.componentInstance;
    mockDepartmentService = TestBed.inject(DepartmentService) as jasmine.SpyObj<DepartmentService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should create', () => {
    mockDepartmentService.getAllDepartments.and.returnValue(of(mockDepartments));
    expect(component).toBeTruthy();
  });

  it('should load departments on init', () => {
    mockDepartmentService.getAllDepartments.and.returnValue(of(mockDepartments));
    
    component.ngOnInit();
    
    expect(mockDepartmentService.getAllDepartments).toHaveBeenCalled();
    expect(component.departments).toEqual(mockDepartments);
    expect(component.loading).toBeFalse();
  });

  it('should handle error when loading departments', () => {
    const errorMessage = 'Failed to load departments';
    mockDepartmentService.getAllDepartments.and.returnValue(throwError(() => new Error(errorMessage)));
    
    component.ngOnInit();
    
    expect(component.error).toBe(errorMessage);
    expect(component.loading).toBeFalse();
  });

  it('should show add form when showAddDepartmentForm is called', () => {
    component.showAddDepartmentForm();
    
    expect(component.showAddForm).toBeTrue();
    expect(component.editingDepartment).toBeNull();
  });

  it('should show edit form with department data when editDepartment is called', () => {
    const department = mockDepartments[0];
    
    component.editDepartment(department);
    
    expect(component.showAddForm).toBeTrue();
    expect(component.editingDepartment).toBe(department);
    expect(component.departmentForm.get('name')?.value).toBe(department.name);
    expect(component.departmentForm.get('description')?.value).toBe(department.description);
  });

  it('should cancel form and reset state', () => {
    component.showAddForm = true;
    component.editingDepartment = mockDepartments[0];
    component.error = 'Some error';
    
    component.cancelForm();
    
    expect(component.showAddForm).toBeFalse();
    expect(component.editingDepartment).toBeNull();
    expect(component.error).toBe('');
  });

  it('should navigate back to admin dashboard', () => {
    component.goBack();
    
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/admin']);
  });
});
