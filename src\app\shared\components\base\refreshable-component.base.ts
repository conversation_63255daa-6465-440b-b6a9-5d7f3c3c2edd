import { <PERSON><PERSON><PERSON><PERSON>, OnInit, Directive } from '@angular/core';
import { DataRefreshService, RefreshableComponent } from '../../../core/services/data-refresh.service';

@Directive()
export abstract class RefreshableComponentBase implements OnInit, OnDestroy, RefreshableComponent {
  protected componentId: string;
  public componentName?: string;

  constructor(
    protected dataRefreshService: DataRefreshService,
    componentId?: string
  ) {
    // Generate unique component ID if not provided
    this.componentId = componentId || this.generateComponentId();
    this.componentName = this.constructor.name;
  }

  ngOnInit(): void {
    // Register this component for automatic refresh
    this.dataRefreshService.registerComponent(this.componentId, this);
    console.log(`🎯 ${this.componentName}: Registered for language change refresh`);
  }

  ngOnDestroy(): void {
    // Unregister this component
    this.dataRefreshService.unregisterComponent(this.componentId);
    console.log(`🎯 ${this.componentName}: Unregistered from language change refresh`);
  }

  // Abstract method that must be implemented by child components
  abstract refreshData(): void;

  // Helper method to generate unique component ID
  private generateComponentId(): string {
    return `${this.constructor.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Helper method to manually trigger refresh
  protected triggerRefresh(): void {
    this.dataRefreshService.refreshComponent(this.componentId);
  }

  // Helper method to check if component is registered
  protected isRegistered(): boolean {
    return this.dataRefreshService.isComponentRegistered(this.componentId);
  }
}
