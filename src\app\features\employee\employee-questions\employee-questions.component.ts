import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { QuestionsService, Question, SurveySubmissionRequest, SurveyResponseItem } from '../../../core/services/questions.service';
import { AuthService } from '../../../core/services/auth.service';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';
import { Employee, EmployeeService } from '../../../core/services/employee.service';
import { DataRefreshService } from '../../../core/services/data-refresh.service';
import { RefreshableComponentBase } from '../../../shared/components/base/refreshable-component.base';
import { TranslatePipe } from '../../../shared/pipes/translate.pipe';

@Component({
  selector: 'app-employee-questions',
  standalone: true,
  imports: [CommonModule, LoadingComponent, TranslatePipe],
  templateUrl: './employee-questions.component.html',
  styleUrls: ['./employee-questions.component.css']
})
export class EmployeeQuestionsComponent extends RefreshableComponentBase implements OnInit {
  questions: Question[] = [];
  responses: { [questionId: string]: string | string[] } = {};
  loading = false;
  submitting = false;
  employeeId: string = '';
  employeeName: string = '';
  currentQuestionIndex = 0;
  showCompletion = false;
  currentEmployee: Employee | null = null;

  constructor(
    private questionsService: QuestionsService,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private employeeService: EmployeeService,
    dataRefreshService: DataRefreshService
  ) {
    super(dataRefreshService, 'EmployeeQuestionsComponent');
  }

  override ngOnInit(): void {
    super.ngOnInit(); // Register for language change refresh
    this.route.queryParams.subscribe(params => {
      this.employeeId = params['employeeId'] || 0;
      this.employeeName = params['employeeName'] || ''
      this.loadQuestions();
      this.loadEmployee();
    });
  }

  // Implement the abstract refreshData method
  refreshData(): void {
    console.log('🔄 EmployeeQuestionsComponent: Refreshing data due to language change');
    this.loadQuestions();
    this.loadEmployee();
  }

  loadEmployee(): void {
    this.loading = true;
    this.employeeService.getEmployeeById(this.employeeId).subscribe({
      next: (employee) => {
        console.log("employee_response", employee);
        this.currentEmployee = employee ?? null;
        this.loading = false;

      },
      error: (error) => {
        console.error('Error loading employee:', error);
        this.loading = false;
      }
    });
  }

  loadQuestions(): void {
    this.loading = true;
    this.questionsService.getAllQuestions().subscribe({
      next: (response) => {
        this.questions = response.questions;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading questions:', error);
        this.loading = false;
      }
    });
  }

  onAnswerSelect(questionId: string, optionText: string): void {
    const question = this.questions.find(q => q.id === questionId);
    const option = question?.options.find(opt => opt.option_text === optionText);

    if (!option) return;

    if (question?.question_type === 'multiple_choice') {
      // Handle multiple choice - store option IDs
      if (!this.responses[questionId]) {
        this.responses[questionId] = [];
      }
      const answers = this.responses[questionId] as string[];
      const index = answers.indexOf(option.id);
      if (index > -1) {
        answers.splice(index, 1);
      } else {
        answers.push(option.id);
      }
    } else {
      // Handle single choice - store option ID
      this.responses[questionId] = option.id;
    }
  }

  isAnswerSelected(questionId: string, optionText: string): boolean {
    const question = this.questions.find(q => q.id === questionId);
    const option = question?.options.find(opt => opt.option_text === optionText);

    if (!option) return false;

    const response = this.responses[questionId];
    if (Array.isArray(response)) {
      return response.includes(option.id);
    }
    return response === option.id;
  }

  nextQuestion(): void {
    if (this.currentQuestionIndex < this.questions.length - 1) {
      this.currentQuestionIndex++;
    } else {
      this.submitSurvey();
    }
  }

  previousQuestion(): void {
    if (this.currentQuestionIndex > 0) {
      this.currentQuestionIndex--;
    }
  }

  canProceed(): boolean {
    const currentQuestion = this.questions[this.currentQuestionIndex];
    if (!currentQuestion) return false;

    const response = this.responses[currentQuestion.id];
    if (Array.isArray(response)) {
      return response.length > 0;
    }
    return !!response;
  }

  submitSurvey(): void {
    this.submitting = true;

    const currentUser = this.authService.getCurrentUser();
    console.log("currentUser", currentUser);
    if (!this.currentEmployee) {
      console.error('No current user found');
      this.submitting = false;
      return;
    }

    // Transform responses to the new API format
    const surveyResponses: SurveyResponseItem[] = [];

    Object.keys(this.responses).forEach(questionId => {
      const response = this.responses[questionId];

      if (Array.isArray(response)) {
        // Multiple choice - create a response for each selected option
        response.forEach(optionId => {
          surveyResponses.push({
            questionId: questionId,
            selectedOptionId: optionId
          });
        });
      } else if (response) {
        // Single choice
        surveyResponses.push({
          questionId: questionId,
          selectedOptionId: response
        });
      }
    });

    console.log("currentEmployee", this.currentEmployee)

    const surveySubmission: SurveySubmissionRequest = {
      userId: this.currentEmployee.id.toString(),
      companyId: this.currentEmployee?.companyId?.toString() ?? '',
      responses: surveyResponses,
      department: this.currentEmployee?.department.name ?? '' // You might want to get this from user profile or make it dynamic
    };

    this.questionsService.submitSurvey(surveySubmission).subscribe({
      next: (response) => {
        console.log('Survey submitted successfully:', response);
        this.submitting = false;
        this.showCompletion = true;
      },
      error: (error: any) => {
        console.error('Error submitting survey:', error);
        this.submitting = false;
      }
    });
  }

  onEmployeeClick(employee: Employee): void {
    console.log("employee==>", employee)
    // this.router.navigate(['/employee/questions'], {
    //   queryParams: { employeeId: employee.id, employeeName: employee.firstName }
    // });
  }

  goHome(): void {
    this.router.navigate(['/employee']);
  }
}
