import { Injectable } from '@angular/core';
import { Observable, interval } from 'rxjs';
import { map, startWith } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class TimeService {

  constructor() { }

  // Get current time as observable that updates every minute
  getCurrentTime(): Observable<string> {
    return interval(60000).pipe(
      startWith(0),
      map(() => this.formatTime(new Date()))
    );
  }

  // Get current time as string
  getCurrentTimeString(): string {
    return this.formatTime(new Date());
  }

  // Format time to display format (e.g., "3:00 PM")
  private formatTime(date: Date): string {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  }

  // Get current date
  getCurrentDate(): string {
    return new Date().toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
