import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService, User } from '../../../core/services/auth.service';
import { TimeService } from '../../../core/services/time.service';
import { LanguageSelectorComponent } from '../language-selector/language-selector.component';
import { TranslatePipe } from '../../pipes/translate.pipe';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule, LanguageSelectorComponent, TranslatePipe],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit, OnDestroy {
  currentUser: User | null = null;
  currentTime: string = '';
  private subscriptions: Subscription[] = [];

  constructor(
    public authService: AuthService,
    private timeService: TimeService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Subscribe to current user
    this.subscriptions.push(
      this.authService.currentUser$.subscribe(user => {
        this.currentUser = user;
      })
    );

    // Subscribe to current time
    this.subscriptions.push(
      this.timeService.getCurrentTime().subscribe(time => {
        this.currentTime = time;
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  navigateToProfile(): void {
    if (this.authService.isEmployee()) {
      this.router.navigate(['/employee/profile']);
    } else {
      this.router.navigate(['/admin/profile']);
    }
  }

  navigateToHome(): void {
    if (this.authService.isEmployee()) {
      this.router.navigate(['/employee']);
    } else {
      this.router.navigate(['/admin']);
    }
  }

  get userDisplayName(): string {
    if (this.currentUser) {
      return `${this.currentUser.firstName} ${this.currentUser.lastName}`.trim();
    }
    return '';
  }
}
