import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class CompanySuperAdminGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    if (this.authService.isAuthenticated() && this.authService.isCompanySuperAdmin()) {
      return true;
    } else if (this.authService.isAuthenticated()) {
      // User is authenticated but not companySuperAdmin - redirect to appropriate page
      if (this.authService.isEmployee()) {
        this.router.navigate(['/employee']);
      } else {
        this.router.navigate(['/admin']);
      }
      return false;
    } else {
      // User is not authenticated
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return false;
    }
  }
}
