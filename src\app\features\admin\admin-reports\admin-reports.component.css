/* Admin Reports Styles */
.card {
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
}

.card-header h5 {
  color: #495057;
}

/* Summary Cards */
.card-body h2 {
  font-weight: 700;
  font-size: 2.5rem;
}

.card-body h5 {
  font-size: 0.9rem;
  font-weight: 600;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Chart Container */
.chart-container {
  position: relative;
  margin: 0 auto;
}

/* Enhanced Filter Styles */
.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-label i {
  font-size: 0.9rem;
}

.form-control, .form-select {
  border: 1.5px solid #e9ecef;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

/* Quick Range Buttons */
.btn-outline-primary.btn-sm {
  font-size: 0.8rem;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.btn-outline-primary.btn-sm:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
}

/* Filter Summary Alert */
.alert-info {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #b3e5fc;
  border-radius: 0.5rem;
}

/* API Test Button */
.btn-outline-info.btn-sm {
  border-width: 1.5px;
  font-weight: 500;
}

.btn-outline-info.btn-sm:hover {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  transform: translateY(-1px);
}

/* Table Styles */
.table th {
  font-weight: 600;
  color: #495057;
  border-top: none;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table td {
  vertical-align: middle;
  font-size: 0.9rem;
}

.table-hover tbody tr:hover {
  background-color: #f8f9fa;
}

/* Badge Styles */
.badge {
  font-size: 0.75rem;
  font-weight: 600;
}

/* Filter Section */
.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-select {
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  font-size: 0.9rem;
}

.form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Button Styles */
.btn {
  font-weight: 600;
  border-radius: 0.375rem;
  transition: all 0.2s ease-in-out;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .card-body h2 {
    font-size: 2rem;
  }

  .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .btn-group {
    width: 100%;
  }

  .btn-group .btn {
    flex: 1;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .chart-container {
    height: 300px !important;
  }
}

@media (max-width: 576px) {
  .card-body h2 {
    font-size: 1.75rem;
  }

  .card-body h5 {
    font-size: 0.8rem;
  }

  .table th,
  .table td {
    padding: 0.5rem;
    font-size: 0.75rem;
  }

  .chart-container {
    height: 250px !important;
  }

  .fs-2 {
    font-size: 1.5rem !important;
  }
}

/* Loading and Empty States */
.text-muted i {
  opacity: 0.5;
}

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

/* Animation for cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card {
  animation: fadeInUp 0.5s ease-out;
}

/* Pagination */
.pagination .page-link {
  color: #0d6efd;
  border: 1px solid #dee2e6;
}

.pagination .page-item.active .page-link {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
}

/* Color indicators */
.badge[style*="background-color"] {
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.text-primary { color: #0099E3 !important; }
.text-success { color: #28a745 !important; }
.text-info { color: #17a2b8 !important; }
.text-warning { color: #ffc107 !important; }
