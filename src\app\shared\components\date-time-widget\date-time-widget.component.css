.date-time-widget {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 153, 227, 0.2);
  border-radius: 12px;
  padding: 15px 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 250px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.time-display {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.date-display {
  display: flex;
  align-items: center;
}

.time {
  font-size: 18px;
  font-weight: 700;
  color: #0099E3;
}

.date {
  font-size: 14px;
  font-weight: 500;
  color: #2E6986;
}

.date-time-widget i {
  color: #0099E3;
  width: 16px;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .date-time-widget {
    top: 10px;
    right: 10px;
    padding: 10px 15px;
    min-width: 200px;
  }
  
  .time {
    font-size: 16px;
  }
  
  .date {
    font-size: 12px;
  }
}

/* Animation */
.date-time-widget {
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Hover effect */
.date-time-widget:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  transition: all 0.3s ease;
}
