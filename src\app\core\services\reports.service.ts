import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, delay } from 'rxjs/operators';
import { AuthService } from './auth.service';

export interface ReportSummary {
  totalResponses: number;
  responseRate: number;
  averageScore: number;
  activeUsers: number;
  totalEligibleUsers: number;
  lastUpdated: string;
}

export interface CategoryItem {
  id: string;
  name: string;
  displayName: string;
  colorCode: string;
  count: number;
  percentage: number;
}

export interface ChartDataset {
  data: number[];
  backgroundColor: string[];
  borderColor: string[];
  borderWidth: number;
}

export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

export interface CategoryDistribution {
  totalResponses: number;
  categories: CategoryItem[];
  chartData: ChartData;
}

export interface TrendAnalysis {
  timeline: any[];
  chartData: ChartData;
}

export interface Department {
  id: string;
  name: string;
  responseCount: number;
  responseRate: number;
  averageScore: number;
  uniqueResponseCount: number;
}

export interface DepartmentAnalytics {
  departments: Department[];
  responseRateChart: ChartData;
  satisfactionChart: ChartData;
}

export interface RecentResponse {
  id: string;
  userId: string;
  userName: string;
  department: string;
  submittedAt: string;
  score: number;
}

export interface Pagination {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

export interface RecentActivity {
  responses: RecentResponse[];
  pagination: Pagination;
}

export interface FilterOption {
  label: string;
  value: string;
}

export interface AvailableFilters {
  departments: FilterOption[];
  dateRanges: FilterOption[];
  users: FilterOption[];
}

export interface UserAccess {
  role: string;
  accessLevel: string;
  canExport: boolean;
  visibleDepartments: string[];
}

export interface SurveyReportsResponse {
  summary: ReportSummary;
  categoryDistribution: CategoryDistribution;
  trendAnalysis: TrendAnalysis;
  departmentAnalytics: DepartmentAnalytics;
  recentActivity: RecentActivity;
  availableFilters: AvailableFilters;
  userAccess: UserAccess;
}

export interface ReportFilters {
  department?: string;
  dateRange?: string;
  userId?: string;
  page?: number;
  limit?: number;
}

export interface DashboardFilters {
  startDate?: string;
  endDate?: string;
  departmentId?: string;
  period?: 'daily' | 'weekly' | 'monthly' | 'yearly';
}

@Injectable({
  providedIn: 'root'
})
export class ReportsService {
  private readonly API_URL = 'http://localhost:3000/api';

  // Mock data based on your API response structure
  private mockReportsData: SurveyReportsResponse = {
    summary: {
      totalResponses: 1247,
      responseRate: 85.3,
      averageScore: 3.8,
      activeUsers: 156,
      totalEligibleUsers: 183,
      lastUpdated: "2025-07-17T22:08:00.000Z"
    },
    categoryDistribution: {
      totalResponses: 1247,
      categories: [
        {
          id: "uuid-1",
          name: "VERY_POSITIVE",
          displayName: "Very Positive",
          colorCode: "#28a745",
          count: 312,
          percentage: 25.0
        },
        {
          id: "uuid-2",
          name: "POSITIVE",
          displayName: "Positive",
          colorCode: "#17a2b8",
          count: 561,
          percentage: 45.0
        },
        {
          id: "uuid-3",
          name: "NEUTRAL",
          displayName: "Neutral",
          colorCode: "#ffc107",
          count: 249,
          percentage: 20.0
        },
        {
          id: "uuid-4",
          name: "NEGATIVE",
          displayName: "Negative",
          colorCode: "#fd7e14",
          count: 100,
          percentage: 8.0
        },
        {
          id: "uuid-5",
          name: "VERY_NEGATIVE",
          displayName: "Very Negative",
          colorCode: "#dc3545",
          count: 25,
          percentage: 2.0
        }
      ],
      chartData: {
        labels: ["Very Positive", "Positive", "Neutral", "Negative", "Very Negative"],
        datasets: [{
          data: [312, 561, 249, 100, 25],
          backgroundColor: ["#28a745", "#17a2b8", "#ffc107", "#fd7e14", "#dc3545"],
          borderColor: ["#28a745", "#17a2b8", "#ffc107", "#fd7e14", "#dc3545"],
          borderWidth: 1
        }]
      }
    },
    trendAnalysis: {
      timeline: [],
      chartData: {
        labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
        datasets: [{
          data: [3.2, 3.5, 3.8, 3.6, 3.9, 3.8],
          backgroundColor: ["#0d6efd"],
          borderColor: ["#0d6efd"],
          borderWidth: 2
        }]
      }
    },
    departmentAnalytics: {
      departments: [
        {
          id: "dept-1",
          name: "Engineering",
          responseCount: 45,
          responseRate: 90.0,
          averageScore: 4.2,
          uniqueResponseCount: 5
        },
        {
          id: "dept-2",
          name: "Marketing",
          responseCount: 32,
          responseRate: 80.0,
          averageScore: 3.8,
          uniqueResponseCount: 5

        },
        {
          id: "dept-3",
          name: "Sales",
          responseCount: 28,
          responseRate: 75.0,
          averageScore: 3.5,
          uniqueResponseCount: 5

        },
        {
          id: "dept-4",
          name: "HR",
          responseCount: 15,
          responseRate: 85.0,
          averageScore: 4.0,
          uniqueResponseCount: 5

        },
        {
          id: "dept-5",
          name: "Finance",
          responseCount: 20,
          responseRate: 88.0,
          averageScore: 3.9,
          uniqueResponseCount: 5
        }
      ],
      responseRateChart: {
        labels: ["Engineering", "Marketing", "Sales", "HR", "Finance"],
        datasets: [{
          data: [90.0, 80.0, 75.0, 85.0, 88.0],
          backgroundColor: ["#28a745", "#17a2b8", "#ffc107", "#fd7e14", "#6f42c1"],
          borderColor: ["#28a745", "#17a2b8", "#ffc107", "#fd7e14", "#6f42c1"],
          borderWidth: 1
        }]
      },
      satisfactionChart: {
        labels: ["Engineering", "Marketing", "Sales", "HR", "Finance"],
        datasets: [{
          data: [4.2, 3.8, 3.5, 4.0, 3.9],
          backgroundColor: ["#28a745", "#17a2b8", "#ffc107", "#fd7e14", "#6f42c1"],
          borderColor: ["#28a745", "#17a2b8", "#ffc107", "#fd7e14", "#6f42c1"],
          borderWidth: 1
        }]
      }
    },
    recentActivity: {
      responses: [
        {
          id: "resp-1",
          userId: "user-1",
          userName: "John Smith",
          department: "Engineering",
          submittedAt: "2025-07-17T20:30:00.000Z",
          score: 4
        },
        {
          id: "resp-2",
          userId: "user-2",
          userName: "Sarah Johnson",
          department: "Marketing",
          submittedAt: "2025-07-17T19:45:00.000Z",
          score: 5
        },
        {
          id: "resp-3",
          userId: "user-3",
          userName: "Mike Davis",
          department: "Sales",
          submittedAt: "2025-07-17T18:20:00.000Z",
          score: 3
        },
        {
          id: "resp-4",
          userId: "user-4",
          userName: "Emily Brown",
          department: "HR",
          submittedAt: "2025-07-17T17:15:00.000Z",
          score: 4
        },
        {
          id: "resp-5",
          userId: "user-5",
          userName: "David Wilson",
          department: "Finance",
          submittedAt: "2025-07-17T16:30:00.000Z",
          score: 5
        }
      ],
      pagination: {
        currentPage: 1,
        totalPages: 5,
        totalItems: 50,
        itemsPerPage: 10
      }
    },
    availableFilters: {
      departments: [
        { label: "Engineering", value: "engineering" },
        { label: "Marketing", value: "marketing" },
        { label: "Sales", value: "sales" },
        { label: "HR", value: "hr" },
        { label: "Finance", value: "finance" }
      ],
      dateRanges: [
        { label: "Last 7 days", value: "7d" },
        { label: "Last 30 days", value: "30d" },
        { label: "Last 3 months", value: "3m" }
      ],
      users: []
    },
    userAccess: {
      role: "CompanyAdmin",
      accessLevel: "company_all",
      canExport: true,
      visibleDepartments: ["all"]
    }
  };

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getToken();
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred!';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      if (error.error && error.error.message) {
        errorMessage = error.error.message;
      }
    }
    
    console.error('Reports Service Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  // getSurveyReports(filters?: ReportFilters): Observable<SurveyReportsResponse> {
  //   // Return mock data for now
  //   console.log('Using mock data for reports. Filters:', filters);

  //   // Simulate filtering if department filter is applied
  //   let filteredData = { ...this.mockReportsData };

  //   if (filters?.department) {
  //     // Filter department analytics
  //     filteredData.departmentAnalytics.departments = this.mockReportsData.departmentAnalytics.departments
  //       .filter(dept => dept.name.toLowerCase() === filters.department?.toLowerCase());

  //     // Filter recent activity
  //     filteredData.recentActivity.responses = this.mockReportsData.recentActivity.responses
  //       .filter(response => response.department.toLowerCase() === filters.department?.toLowerCase());
  //   }

  //   return of(filteredData).pipe(delay(500));

  //   // Uncomment below to use real API
  //   /*
  //   const headers = this.getAuthHeaders();
  //   let url = `${this.API_URL}/reports/survey`;

  //   if (filters) {
  //     const params = new URLSearchParams();
  //     Object.entries(filters).forEach(([key, value]) => {
  //       if (value !== undefined && value !== null) {
  //         params.append(key, value.toString());
  //       }
  //     });
  //     if (params.toString()) {
  //       url += `?${params.toString()}`;
  //     }
  //   }

  //   return this.http.get<SurveyReportsResponse>(url, { headers })
  //     .pipe(catchError(this.handleError));
  //   */
  // }

  /**
   * Get dashboard reports with date range and department filtering
   * Integrates with the /api/reports/dashboard endpoint
   */
  getDashboardReports(filters?: DashboardFilters): Observable<SurveyReportsResponse> {
    const headers = this.getAuthHeaders();
    let url = `${this.API_URL}/reports/dashboard`;

    // Build query parameters
    const params = new URLSearchParams();

    if (filters?.startDate) {
      // params.append('startDate', filters.startDate);
    }

    if (filters?.endDate) {
      // params.append('endDate', filters.endDate);
    }

    if (filters?.departmentId) {
      params.append('departmentId', filters.departmentId);
    }

    if (filters?.period) {
      params.append('period', filters.period);
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }

    console.log('Calling dashboard API:', url);

    return this.http.get<SurveyReportsResponse>(url, { headers })
      .pipe(
        catchError((error) => {
          console.error('Dashboard API Error:', error);
          // Fallback to mock data on error
          console.log('Falling back to mock data due to API error');
          return of(this.mockReportsData).pipe(delay(500));
        })
      );
  }

  exportReport(format: 'pdf' | 'excel' = 'pdf', filters?: ReportFilters): Observable<Blob> {
    // Return mock blob for now
    console.log(`Mock export ${format} with filters:`, filters);

    const mockContent = `Mock ${format.toUpperCase()} Report\n\nSurvey Reports Export\nGenerated: ${new Date().toLocaleString()}\n\nTotal Responses: 1247\nResponse Rate: 85.3%\nAverage Score: 3.8/5`;
    const blob = new Blob([mockContent], {
      type: format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    return of(blob).pipe(delay(1000));

    // Uncomment below to use real API
    /*
    const headers = this.getAuthHeaders();
    let url = `${this.API_URL}/reports/export/${format}`;

    if (filters) {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
    }

    return this.http.get(url, {
      headers,
      responseType: 'blob'
    }).pipe(catchError(this.handleError));
    */
  }
}
