<div class="role-debug-panel">
  <div class="card">
    <div class="card-header bg-info text-white">
      <h5 class="mb-0">
        <i class="fas fa-user-shield me-2"></i>
        Role-Based Access Control Debug Panel
      </h5>
    </div>
    <div class="card-body">
      <!-- Current User Info -->
      <div class="row mb-4">
        <div class="col-md-6">
          <h6 class="text-primary">Current User Information:</h6>
          <div class="user-info">
            <p><strong>Authenticated:</strong> 
              <span [class]="isAuthenticated ? 'text-success' : 'text-danger'">
                <i [class]="isAuthenticated ? 'fas fa-check' : 'fas fa-times'"></i>
                {{ isAuthenticated ? 'Yes' : 'No' }}
              </span>
            </p>
            <p><strong>User Role:</strong> 
              <span class="badge bg-primary">{{ userRole }}</span>
            </p>
            <p><strong>Is Employee:</strong> 
              <span [class]="isEmployee ? 'text-success' : 'text-warning'">
                <i [class]="isEmployee ? 'fas fa-check' : 'fas fa-times'"></i>
                {{ isEmployee ? 'Yes' : 'No' }}
              </span>
            </p>
            <p *ngIf="currentUser"><strong>Email:</strong> {{ currentUser.email }}</p>
            <p *ngIf="currentUser"><strong>Company ID:</strong> {{ currentUser.companyId }}</p>
          </div>
        </div>
        <div class="col-md-6">
          <h6 class="text-primary">Access Permissions:</h6>
          <div class="permissions">
            <p>
              <i class="fas fa-users me-2 text-primary"></i>
              <strong>Employee Routes:</strong> 
              <span [class]="isEmployee ? 'text-success' : 'text-danger'">
                {{ isEmployee ? 'Allowed' : 'Denied' }}
              </span>
            </p>
            <p>
              <i class="fas fa-user-shield me-2 text-warning"></i>
              <strong>Admin Routes:</strong> 
              <span [class]="!isEmployee && isAuthenticated ? 'text-success' : 'text-danger'">
                {{ !isEmployee && isAuthenticated ? 'Allowed' : 'Denied' }}
              </span>
            </p>
            <p>
              <i class="fas fa-building me-2 text-info"></i>
              <strong>Department Management:</strong> 
              <span [class]="userRole === 'companySuperAdmin' ? 'text-success' : 'text-danger'">
                {{ userRole === 'companySuperAdmin' ? 'Allowed' : 'Denied' }}
              </span>
            </p>
            <p>
              <i class="fas fa-users-cog me-2 text-secondary"></i>
              <strong>User Management:</strong> 
              <span [class]="userRole === 'companySuperAdmin' ? 'text-success' : 'text-danger'">
                {{ userRole === 'companySuperAdmin' ? 'Allowed' : 'Denied' }}
              </span>
            </p>
          </div>
        </div>
      </div>

      <!-- Route Testing Buttons -->
      <div class="row" *ngIf="isAuthenticated">
        <div class="col-12">
          <h6 class="text-primary">Test Route Access:</h6>
          <div class="btn-group-vertical w-100" role="group">
            <button 
              type="button" 
              class="btn btn-outline-primary mb-2"
              (click)="testEmployeeRoute()">
              <i class="fas fa-users me-2"></i>
              Test Employee Dashboard Access
            </button>
            <button 
              type="button" 
              class="btn btn-outline-warning mb-2"
              (click)="testAdminRoute()">
              <i class="fas fa-user-shield me-2"></i>
              Test Admin Dashboard Access
            </button>
            <button 
              type="button" 
              class="btn btn-outline-info mb-2"
              (click)="testDepartmentRoute()">
              <i class="fas fa-building me-2"></i>
              Test Department Management Access
            </button>
            <button 
              type="button" 
              class="btn btn-outline-secondary mb-2"
              (click)="testUserManagementRoute()">
              <i class="fas fa-users-cog me-2"></i>
              Test User Management Access
            </button>
          </div>
        </div>
      </div>

      <!-- Login Links -->
      <div class="row mt-4" *ngIf="!isAuthenticated">
        <div class="col-12">
          <h6 class="text-primary">Login Options:</h6>
          <div class="d-flex gap-2">
            <a routerLink="/employee-login" class="btn btn-primary">
              <i class="fas fa-users me-2"></i>
              Employee Login
            </a>
            <a routerLink="/login" class="btn btn-warning">
              <i class="fas fa-user-shield me-2"></i>
              Admin Login
            </a>
          </div>
        </div>
      </div>

      <!-- Logout Button -->
      <div class="row mt-4" *ngIf="isAuthenticated">
        <div class="col-12">
          <button 
            type="button" 
            class="btn btn-danger"
            (click)="logout()">
            <i class="fas fa-sign-out-alt me-2"></i>
            Logout
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
