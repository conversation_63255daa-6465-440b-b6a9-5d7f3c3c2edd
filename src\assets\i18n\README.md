# Multi-Language Support (i18n) Setup

This directory contains translation files for the VE Employee Survey System's internationalization (i18n) support.

## 📁 File Structure

```
src/assets/i18n/
├── en.json     # English (default)
├── es.json     # Spanish
├── fr.json     # French
├── ar.json     # Arabic (RTL support)
├── de.json     # German (to be added)
├── zh.json     # Chinese (to be added)
├── hi.json     # Hindi (to be added)
└── pt.json     # Portuguese (to be added)
```

## 🌍 Supported Languages

| Language | Code | File | Status | RTL |
|----------|------|------|--------|-----|
| English | `en` | `en.json` | ✅ Complete | No |
| Spanish | `es` | `es.json` | ✅ Complete | No |
| French | `fr` | `fr.json` | ✅ Complete | No |
| Arabic | `ar` | `ar.json` | ✅ Complete | Yes |
| German | `de` | `de.json` | ⏳ Pending | No |
| Chinese | `zh` | `zh.json` | ⏳ Pending | No |
| Hindi | `hi` | `hi.json` | ⏳ Pending | No |
| Portuguese | `pt` | `pt.json` | ⏳ Pending | No |

## 📝 Translation File Structure

Each translation file follows this hierarchical structure:

```json
{
  "common": {
    "loading": "Loading...",
    "error": "Error",
    "success": "Success"
  },
  "auth": {
    "login": "Login",
    "logout": "Logout",
    "email": "Email Address"
  },
  "navigation": {
    "home": "Home",
    "dashboard": "Dashboard"
  }
}
```

### Main Categories:

- **common**: General UI elements (buttons, messages, etc.)
- **auth**: Authentication related text
- **navigation**: Menu and navigation items
- **employee**: Employee-specific features
- **survey**: Survey-related content
- **admin**: Admin panel content
- **reports**: Reports and analytics
- **messages**: System messages and notifications
- **validation**: Form validation messages
- **time**: Date and time related text

## 🔧 Usage in Components

### 1. Import the Translation Pipe

```typescript
import { TranslatePipe } from '../../../shared/pipes/translate.pipe';

@Component({
  // ...
  imports: [CommonModule, TranslatePipe],
  // ...
})
```

### 2. Use in Templates

```html
<!-- Simple translation -->
<h1>{{ 'auth.login' | translate }}</h1>

<!-- Translation with parameters -->
<p>{{ 'validation.minLength' | translate: {min: 6} }}</p>

<!-- Translation in attributes -->
<button [attr.aria-label]="'common.close' | translate">
```

### 3. Use in Components (TypeScript)

```typescript
import { TranslationService } from '../../../core/services/translation.service';

constructor(private translationService: TranslationService) {}

// Get translation
const message = this.translationService.translate('common.loading');

// Get translation with parameters
const error = this.translationService.translate('validation.minLength', { min: 6 });

// Subscribe to translation changes
this.translationService.getTranslation('auth.login').subscribe(text => {
  console.log(text);
});
```

## 🎛️ Language Switching

### Language Selector Component

The `LanguageSelectorComponent` is already integrated into the header and provides:

- Dropdown with all supported languages
- Flag icons for visual identification
- Automatic language persistence
- RTL support for Arabic

### Programmatic Language Change

```typescript
import { TranslationService } from '../../../core/services/translation.service';

// Change language
await this.translationService.setLanguage('es');

// Get current language
const currentLang = this.translationService.getCurrentLanguage();

// Get language info
const langInfo = this.translationService.getCurrentLanguageInfo();
```

## 🌐 RTL (Right-to-Left) Support

Arabic language includes RTL support:

- Automatic direction change (`dir="rtl"`)
- CSS adjustments for RTL layout
- Proper text alignment
- Icon and layout mirroring

## 📋 Adding New Languages

### 1. Create Translation File

1. Copy `en.json` to `[language-code].json`
2. Translate all values (keep keys unchanged)
3. Update date/time formats if needed

### 2. Update Supported Languages

In `src/app/core/services/translation.service.ts`:

```typescript
public readonly supportedLanguages: Language[] = [
  // ... existing languages
  { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
  { code: 'zh', name: '中文', flag: '🇨🇳' },
];
```

### 3. Test the New Language

1. Start the application
2. Use the language selector
3. Verify all text is translated
4. Check RTL layout if applicable

## 🔍 Translation Keys Reference

### Common Patterns

- Use dot notation: `category.subcategory.key`
- Keep keys descriptive: `auth.invalidCredentials`
- Use camelCase for multi-word keys: `languageChangeNote`
- Group related translations: `validation.*`

### Parameter Placeholders

Use `{{paramName}}` for dynamic values:

```json
{
  "validation": {
    "minLength": "Minimum length is {{min}} characters",
    "fileSize": "File size must be less than {{size}}MB"
  }
}
```

## 🚀 Best Practices

1. **Consistency**: Use the same translation keys across similar components
2. **Context**: Provide enough context in key names
3. **Fallbacks**: Always provide English fallback
4. **Testing**: Test with longer text (German) and RTL (Arabic)
5. **Pluralization**: Consider plural forms for count-based text
6. **Cultural**: Adapt date/time formats per locale

## 🐛 Troubleshooting

### Translation Not Showing

1. Check if the key exists in the translation file
2. Verify the translation file is valid JSON
3. Ensure the TranslatePipe is imported
4. Check browser console for errors

### Language Not Loading

1. Verify the language file exists
2. Check the file path in dynamic imports
3. Ensure the language is in supportedLanguages array
4. Clear browser cache

### RTL Issues

1. Check if `dir="rtl"` is set on `<html>`
2. Verify CSS supports RTL layouts
3. Test with Arabic language
4. Check for hardcoded left/right values

## 📚 Additional Resources

- [Angular i18n Guide](https://angular.io/guide/i18n)
- [MDN Internationalization](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl)
- [RTL CSS Guidelines](https://rtlstyling.com/)
