<div class="d-flex justify-content-between align-items-center mb-4">
  <button class="btn btn-outline-secondary" (click)="goBack()">
    <i class="fas fa-arrow-left me-2"></i>
  </button>
  <h1 class="mb-0"><i class="fas fa-chart-bar me-2"></i>{{ 'reports.surveyReports' | translate }}</h1>

  <div class="btn-group">
  </div>
</div>

<app-loading *ngIf="loading" message="Loading report data..."></app-loading>

<div *ngIf="!loading && reportData">
  <!-- Enhanced Filters -->
  <div class="card mb-4">
    <div class="card-header bg-light">
      <h5 class="mb-0">
        <i class="fas fa-filter me-2 text-primary"></i>
        Report Filters
      </h5>
    </div>
    <div class="card-body">
      <div class="row g-3">
        <div class="col-lg-3 col-md-6">
          <label class="form-label fw-semibold">
            <i class="fas fa-calendar-alt me-1 text-primary"></i>
            Start Date
          </label>
          <input
            type="date"
            class="form-control"
            [(ngModel)]="startDate"
            (change)="onDateRangeChange()"
            max="{{ endDate }}">
        </div>
        <div class="col-lg-3 col-md-6">
          <label class="form-label fw-semibold">
            <i class="fas fa-calendar-alt me-1 text-primary"></i>
            End Date
          </label>
          <input
            type="date"
            class="form-control"
            [(ngModel)]="endDate"
            (change)="onDateRangeChange()"
            min="{{ startDate }}">
        </div>

        <div class="col-lg-3 col-md-6">
          <label class="form-label fw-semibold">
            <i class="fas fa-building me-1 text-success"></i>
            Department
          </label>
          <select class="form-select" [(ngModel)]="selectedDepartment" (change)="onFilterChange()">
            <option value="">All Departments</option>
            <option *ngFor="let dept of reportData.availableFilters.departments" [value]="dept.value">
              {{ dept.label }}
            </option>
          </select>
        </div>

        <div class="col-lg-3 col-md-6">
          <label class="form-label fw-semibold">
            <i class="fas fa-chart-line me-1 text-info"></i>
            Period
          </label>
          <select class="form-select" [(ngModel)]="selectedPeriod" (change)="onPeriodChange()">
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
          </select>
        </div>
      </div>

      <div class="row mt-3">
        <div class="col-12">
          <div class="d-flex flex-wrap align-items-center justify-content-between">
            <div class="d-flex flex-wrap gap-2 mb-2 mb-md-0">
              <small class="text-muted me-2 align-self-center">Quick ranges:</small>
              <button type="button" class="btn btn-outline-primary btn-sm" (click)="setLast7Days()">
                Last 7 days
              </button>
              <button type="button" class="btn btn-outline-primary btn-sm" (click)="setLast30Days()">
                Last 30 days
              </button>
              <button type="button" class="btn btn-outline-primary btn-sm" (click)="setLast90Days()">
                Last 90 days
              </button>
            </div>
            <small class="text-muted">
              <i class="fas fa-clock me-1"></i>
              Last updated: {{ formatDate(reportData.summary.lastUpdated) }}
            </small>
          </div>
        </div>
      </div>

      <div class="row mt-2">
        <div class="col-12">
          <div class="alert alert-info py-2 mb-0">
            <small>
              <i class="fas fa-info-circle me-1"></i>
              {{ getFilterSummary() }}
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center justify-content-center mb-2">
            <i class="fas fa-users text-primary fs-2 me-2"></i>
            <div>
              <h5 class="card-title mb-0">Total Responses</h5>
              <h2 class="text-primary mb-0">{{ reportData.categoryDistribution.totalResponses }}</h2>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center justify-content-center mb-2">
            <i class="fas fa-chart-line text-success fs-2 me-2"></i>
            <div>
              <h5 class="card-title mb-0">Response Rate</h5>
              <h2 class="text-success mb-0">{{ formatPercentage(reportData.summary.responseRate) }}</h2>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center justify-content-center mb-2">
            <i class="fas fa-star text-warning fs-2 me-2"></i>
            <div>
              <h5 class="card-title mb-0">Average Score</h5>
              <h2 class="text-warning mb-0">{{ reportData.summary.averageScore }}/5</h2>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center border-0 shadow-sm">
        <div class="card-body">
          <div class="d-flex align-items-center justify-content-center mb-2">
            <i class="fas fa-user-check text-info fs-2 me-2"></i>
            <div>
              <h5 class="card-title mb-0">Active Users</h5>
              <h2 class="text-info mb-0">{{ reportData.summary.activeUsers }}</h2>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts Row -->
  <div class="row mb-4">
    <!-- Category Distribution Pie Chart -->
    <div class="col-lg-6 mb-4">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">
            <i class="fas fa-chart-pie me-2 text-primary"></i>
            Response Distribution
          </h5>
        </div>
        <div class="card-body">
          <app-chart
            *ngIf="categoryChartData"
            type="pie"
            [data]="categoryChartData"
            height="350px">
          </app-chart>
          <div *ngIf="!categoryChartData" class="text-center text-muted py-5">
            <i class="fas fa-chart-pie fa-3x mb-3"></i>
            <p>No data available</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Department Response Rate Bar Chart -->
    <div class="col-lg-6 mb-4">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">
            <i class="fas fa-chart-bar me-2 text-success"></i>
            Department Response Rates
          </h5>
        </div>
        <div class="card-body">
          <app-chart
            *ngIf="departmentResponseRateData"
            type="bar"
            [data]="departmentResponseRateData"
            height="350px">
          </app-chart>
          <div *ngIf="!departmentResponseRateData" class="text-center text-muted py-5">
            <i class="fas fa-chart-bar fa-3x mb-3"></i>
            <p>No data available</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Second Charts Row -->
  <div class="row mb-4">
    <!-- Department Satisfaction Chart -->
    <div class="col-lg-6 mb-4">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">
            <i class="fas fa-chart-bar me-2 text-warning"></i>
            Department Satisfaction Scores
          </h5>
        </div>
        <div class="card-body">
          <app-chart
            *ngIf="departmentSatisfactionData"
            type="bar"
            [data]="departmentSatisfactionData"
            height="350px">
          </app-chart>
          <div *ngIf="!departmentSatisfactionData" class="text-center text-muted py-5">
            <i class="fas fa-chart-bar fa-3x mb-3"></i>
            <p>No data available</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Trend Analysis Line Chart -->
    <!-- <div class="col-lg-6 mb-4">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">
            <i class="fas fa-chart-line me-2 text-info"></i>
            Trend Analysis
          </h5>
        </div>
        <div class="card-body">
          <app-chart
            *ngIf="trendChartData"
            type="line"
            [data]="trendChartData"
            height="350px">
          </app-chart>
          <div *ngIf="!trendChartData" class="text-center text-muted py-5">
            <i class="fas fa-chart-line fa-3x mb-3"></i>
            <p>No trend data available</p>
          </div>
        </div>
      </div>
    </div> -->
  </div>

  <!-- Category Distribution Table -->
  <div class="row mb-4">
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">
            <i class="fas fa-table me-2 text-primary"></i>
            Response Categories
          </h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>Category</th>
                  <th>Count</th>
                  <th>Percentage</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let category of reportData.categoryDistribution.categories">
                  <td>
                    <span class="badge me-2" [style.background-color]="category.colorCode">
                      &nbsp;
                    </span>
                    {{ category.displayName }}
                  </td>
                  <td>{{ category.count }}</td>
                  <td>{{ formatPercentage(category.percentage) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Department Analytics Table -->
    <div class="col-lg-6">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-light">
          <h5 class="mb-0">
            <i class="fas fa-building me-2 text-success"></i>
            Department Analytics
          </h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-hover">
              <thead class="table-light">
                <tr>
                  <th>Department</th>
                  <th>Responses</th>
                  <th>Rate</th>
                  <th>Avg Score</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let dept of reportData.departmentAnalytics.departments">
                  <td>{{ dept.name }}</td>
                  <td>{{ dept.uniqueResponseCount }}</td>
                  <td>{{ formatPercentage(dept.responseRate) }}</td>
                  <td>
                    <span class="badge bg-warning">{{ dept.averageScore }}/5</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <!-- <div class="card border-0 shadow-sm">
    <div class="card-header bg-light">
      <h5 class="mb-0">
        <i class="fas fa-clock me-2 text-info"></i>
        Recent Survey Responses
      </h5>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead class="table-light">
            <tr>
              <th>User</th>
              <th>Department</th>
              <th>Score</th>
              <th>Submitted</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let response of reportData.recentActivity.responses">
              <td>{{ response.userName }}</td>
              <td>{{ response.department }}</td>
              <td>
                <span class="badge bg-primary">{{ response.score }}/5</span>
              </td>
              <td>{{ formatDate(response.submittedAt) }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <nav *ngIf="reportData.recentActivity.pagination.totalPages > 1" class="mt-3">
        <ul class="pagination justify-content-center">
          <li class="page-item" [class.disabled]="reportData.recentActivity.pagination.currentPage === 1">
            <a class="page-link" href="#" (click)="$event.preventDefault()">Previous</a>
          </li>
          <li class="page-item active">
            <span class="page-link">
              {{ reportData.recentActivity.pagination.currentPage }} of {{ reportData.recentActivity.pagination.totalPages }}
            </span>
          </li>
          <li class="page-item" [class.disabled]="reportData.recentActivity.pagination.currentPage === reportData.recentActivity.pagination.totalPages">
            <a class="page-link" href="#" (click)="$event.preventDefault()">Next</a>
          </li>
        </ul>
      </nav>
    </div>
  </div> -->
</div>
