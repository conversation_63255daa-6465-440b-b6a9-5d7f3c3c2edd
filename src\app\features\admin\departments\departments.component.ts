import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { DepartmentService, Department, CreateDepartmentRequest, UpdateDepartmentRequest } from '../../../core/services/department.service';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

@Component({
  selector: 'app-departments',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, LoadingComponent],
  templateUrl: './departments.component.html',
  styleUrls: ['./departments.component.css']
})
export class DepartmentsComponent implements OnInit {
  departments: Department[] = [];
  loading = false;
  error = '';
  success = '';
  showAddForm = false;
  editingDepartment: Department | null = null;

  departmentForm: FormGroup;

  constructor(
    private departmentService: DepartmentService,
    private formBuilder: FormBuilder,
    private router: Router
  ) {
    this.departmentForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(100)]],
      description: ['', [Validators.required, Validators.minLength(5), Validators.maxLength(500)]]
    });
  }

  ngOnInit(): void {
    this.loadDepartments();
  }

  loadDepartments(): void {
    this.loading = true;
    this.error = '';

    this.departmentService.getAllDepartments().subscribe({
      next: (departments) => {
        this.departments = departments;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Failed to load departments';
        this.loading = false;
      }
    });
  }

  showAddDepartmentForm(): void {
    this.showAddForm = true;
    this.editingDepartment = null;
    this.departmentForm.reset();
    this.error = '';
    this.success = '';
  }

  editDepartment(department: Department): void {
    this.editingDepartment = department;
    this.showAddForm = true;
    this.departmentForm.patchValue({
      name: department.name,
      description: department.description
    });
    this.error = '';
    this.success = '';
  }

  cancelForm(): void {
    this.showAddForm = false;
    this.editingDepartment = null;
    this.departmentForm.reset();
    this.error = '';
    this.success = '';
  }

  onSubmit(): void {
    if (this.departmentForm.invalid) {
      return;
    }

    const formData = this.departmentForm.value;
    this.loading = true;
    this.error = '';

    if (this.editingDepartment) {
      // Update existing department
      const updateData: UpdateDepartmentRequest = {
        name: formData.name,
        description: formData.description
      };

      this.departmentService.updateDepartment(this.editingDepartment.id!, updateData).subscribe({
        next: (updatedDepartment) => {
          this.success = 'Department updated successfully';
          this.loadDepartments();
          this.cancelForm();
        },
        error: (error) => {
          this.error = error.message || 'Failed to update department';
          this.loading = false;
        }
      });
    } else {
      // Create new department
      const createData: CreateDepartmentRequest = {
        name: formData.name,
        description: formData.description
      };

      this.departmentService.createDepartment(createData).subscribe({
        next: (newDepartment) => {
          this.success = 'Department created successfully';
          this.loadDepartments();
          this.cancelForm();
        },
        error: (error) => {
          this.error = error.message || 'Failed to create department';
          this.loading = false;
        }
      });
    }
  }

  deleteDepartment(department: Department): void {
    this.departmentService.deleteDepartment(department.id!).subscribe({
      next: () => {
        this.success = 'Department deleted successfully';
        this.loadDepartments();
      },
      error: (error) => {
        this.error = error.message || 'Failed to delete department';
        this.loading = false;
      }
    });

  }

  goBack(): void {
    this.router.navigate(['/admin']);
  }

  get f() {
    return this.departmentForm.controls;
  }
}
