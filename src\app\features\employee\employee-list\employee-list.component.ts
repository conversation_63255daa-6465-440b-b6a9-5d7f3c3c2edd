import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { EmployeeService, Employee } from '../../../core/services/employee.service';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';
import { DataRefreshService } from '../../../core/services/data-refresh.service';
import { RefreshableComponentBase } from '../../../shared/components/base/refreshable-component.base';

@Component({
  selector: 'app-employee-list',
  standalone: true,
  imports: [CommonModule, FormsModule, LoadingComponent],
  templateUrl: './employee-list.component.html',
  styleUrls: ['./employee-list.component.css']
})
export class EmployeeListComponent extends RefreshableComponentBase implements OnInit {
  employees: Employee[] = [];
  loading = false;
  selectedLetter = '';
  selectedDepartment = '';
  isActiveFilter = true;
  apiTestResult = '';
  showApiTest = false;

  constructor(
    private employeeService: EmployeeService,
    private router: Router,
    private route: ActivatedRoute,
    dataRefreshService: DataRefreshService
  ) {
    super(dataRefreshService, 'EmployeeListComponent');
  }

  override ngOnInit(): void {
    super.ngOnInit(); // Register for language change refresh
    this.route.queryParams.subscribe(params => {
      this.selectedLetter = params['letter'] || '';
      this.loadEmployees();
    });
  }

  // Implement the abstract refreshData method
  refreshData(): void {
    console.log('🔄 EmployeeListComponent: Refreshing data due to language change');
    this.loadEmployees();
  }

  loadEmployees(): void {
    this.loading = true;

    if (this.selectedLetter) {
      console.log(`Loading employees starting with letter: ${this.selectedLetter}`);
      this.employeeService.getEmployeesByLetter(this.selectedLetter).subscribe({
        next: (employees) => {
          console.log("employees", employees);
          // Ensure we always have an array
          this.employees = Array.isArray(employees) ? employees : [];
          this.loading = false;
          console.log(`Loaded ${this.employees.length} employees starting with ${this.selectedLetter}`);
        },
        error: (error) => {
          console.error('Error loading employees by letter:', error);
          this.loading = false;
        }
      });
    } else {
      console.log('Loading all employees');
      this.employeeService.getAllEmployees().subscribe({
        next: (employees) => {
          // Ensure we always have an array
          this.employees = Array.isArray(employees) ? employees : [];
          this.loading = false;
          console.log(`Loaded ${this.employees.length} total employees`);
        },
        error: (error) => {
          console.error('Error loading all employees:', error);
          this.loading = false;
        }
      });
    }
  }

  // Test the new API integration
  testEmployeeAPI(): void {
    this.showApiTest = true;
    this.apiTestResult = 'Testing API connection...';

    this.employeeService.testEmployeeAPI().subscribe({
      next: (result) => {
        this.apiTestResult = `API Test Successful! Found ${result.length} employees.`;
        console.log('API Test Result:', result);
      },
      error: (error) => {
        this.apiTestResult = `API Test Failed: ${error.message}`;
        console.error('API Test Error:', error);
      }
    });
  }

  // Test getting employee by ID
  testGetEmployeeById(): void {
    // Use the first employee's ID for testing, or a sample UUID
    const testId = this.employees.length > 0
      ? this.employees[0].id.toString()
      : '123e4567-e89b-12d3-a456-************'; // Sample UUID from your curl example

    this.showApiTest = true;
    this.apiTestResult = `Testing Get Employee By ID API with ID: ${testId}...`;

    this.employeeService.testGetEmployeeById(testId).subscribe({
      next: (employee) => {
        if (employee) {
          this.apiTestResult = `Get Employee By ID Test Successful! Found: ${employee.name} (${employee.email})`;
          console.log('Get Employee By ID Test Result:', employee);
        } else {
          this.apiTestResult = `Get Employee By ID Test: No employee found with ID ${testId}`;
        }
      },
      error: (error) => {
        this.apiTestResult = `Get Employee By ID Test Failed: ${error.message}`;
        console.error('Get Employee By ID Test Error:', error);
      }
    });
  }

  // Test getting current user as employee
  testGetCurrentUser(): void {
    this.showApiTest = true;
    this.apiTestResult = 'Testing Get Current User API...';

    this.employeeService.getCurrentUserAsEmployee().subscribe({
      next: (employee) => {
        if (employee) {
          this.apiTestResult = `Current User Test Successful! User: ${employee.name} (${employee.email})`;
          console.log('Current User Test Result:', employee);
        } else {
          this.apiTestResult = 'Current User Test: No current user found';
        }
      },
      error: (error) => {
        this.apiTestResult = `Current User Test Failed: ${error.message}`;
        console.error('Current User Test Error:', error);
      }
    });
  }

  // Search employees by department
  searchByDepartment(): void {
    if (!this.selectedDepartment) {
      this.loadEmployees();
      return;
    }

    this.loading = true;
    console.log(`Searching employees in department: ${this.selectedDepartment}`);

    this.employeeService.getEmployeesByDepartment(this.selectedDepartment, this.isActiveFilter).subscribe({
      next: (employees) => {
        // Ensure we always have an array
        this.employees = Array.isArray(employees) ? employees : [];
        this.loading = false;
        console.log(`Found ${this.employees.length} employees in ${this.selectedDepartment} department`);
      },
      error: (error) => {
        console.error('Error searching by department:', error);
        this.loading = false;
      }
    });
  }

  // Advanced search using the comprehensive API method
  advancedSearch(): void {
    this.loading = true;
    console.log('Performing advanced search with filters:', {
      letter: this.selectedLetter,
      department: this.selectedDepartment,
      isActive: this.isActiveFilter
    });

    this.employeeService.getCompanyEmployees(
      undefined, // Use current company ID
      this.selectedLetter || undefined,
      this.isActiveFilter,
      this.selectedDepartment || undefined
    ).subscribe({
      next: (employees) => {
        // Ensure we always have an array
        this.employees = Array.isArray(employees) ? employees : [];
        this.loading = false;
        console.log(`Advanced search found ${this.employees.length} employees`);
      },
      error: (error) => {
        console.error('Error in advanced search:', error);
        this.loading = false;
      }
    });
  }

  onEmployeeClick(employee: Employee): void {
    console.log("employee_list", employee);
    console.log("employee_list_hassubmitted", employee.hasSubmittedSurveyToday);

    if (!employee.hasSubmittedSurveyToday) {
      // Employee hasn't submitted survey today, proceed to questions
      this.router.navigate(['/employee/questions'], {
        queryParams: { employeeId: employee.id, employeeName: employee.firstName || employee.name }
      });
    } else {
      // Employee has already submitted survey today, show toast message
      const employeeName = employee.firstName || employee.name || 'This employee';

      // Optional: Also log for debugging
      console.log(`${employeeName} has already submitted their survey today`);
    }
  }

  goBack(): void {
    this.router.navigate(['/employee']);
  }

  
}
