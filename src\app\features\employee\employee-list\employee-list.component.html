<div class="d-flex justify-content-between align-items-center mb-4">
 <div>
   <button class="btn btn-outline-secondary" (click)="goBack()">
     <i class="fas fa-arrow-left me-2"></i>
   </button>
 </div>
  <div class="text-center">
    <h2 *ngIf="selectedLetter" class="mb-0">
      <i class="fas fa-users me-2 text-primary"></i>
      Employees starting with "{{ selectedLetter }}"
    </h2>
    <h2 *ngIf="!selectedLetter" class="mb-0">
      <i class="fas fa-users me-2 text-primary"></i>
      All Employees
    </h2>
  </div>
  <div></div>
  
</div>

<app-loading *ngIf="loading" message="Loading employees..."></app-loading>
<div class="mt-3" *ngIf="!loading">
  <div
    *ngFor="let employee of employees"
    class="employee-item"
    [ngClass]="{
      'survey-completed': employee.hasSubmittedSurveyToday,
      'survey-pending': !employee.hasSubmittedSurveyToday
    }"
    (click)="onEmployeeClick(employee)"
    style="cursor: pointer;">

    <div class="employee-info">
      <div class="employee-name">
        <i class="fas fa-user me-2 text-primary"></i>
        {{ employee.firstName || employee.name }} {{ employee.lastName || '' }}
      </div>
      <div class="employee-details">
        <small class="text-muted">
          <i class="fas fa-envelope me-1"></i>{{ employee.email }}
          <span class="ms-3">
            <i class="fas fa-building me-1"></i>{{ employee.department.name || employee.department }}
          </span>
        </small>
      </div>
    </div>

    <div class="survey-status">
      <div *ngIf="employee.hasSubmittedSurveyToday" class="status-badge completed">
        <i class="fas fa-check-circle me-1"></i>
        <span>Survey Completed</span>
      </div>
      <div *ngIf="!employee.hasSubmittedSurveyToday" class="status-badge pending">
        <i class="fas fa-clock me-1"></i>
        <span>Survey Pending</span>
      </div>
    </div>
  </div>

  <div *ngIf="employees.length === 0" class="text-center mt-5">
    <div class="empty-state">
      <i class="fas fa-users fa-3x text-muted mb-3"></i>
      <p class="text-muted">No employees found{{ selectedLetter ? ' starting with "' + selectedLetter + '"' : '' }}.</p>
    </div>
  </div>
</div>

