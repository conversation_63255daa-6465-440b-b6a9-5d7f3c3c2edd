import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import {
  UserManagementService,
  User,
  CreateUserRequest,
  UpdateUserRequest,
  Role,
  Department as UserDepartment,
  Supervisor,
  CreateUserResponse
} from '../../../core/services/user-management.service';
import { DepartmentService, Department as ServiceDepartment } from '../../../core/services/department.service';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';
import { forkJoin } from 'rxjs';
import { NgSelectModule } from '@ng-select/ng-select';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, LoadingComponent, NgSelectModule],
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css']
})
export class UserManagementComponent implements OnInit {
  users: User[] = [];
  roles: Role[] = [];
  departments: ServiceDepartment[] = [];
  supervisors: Supervisor[] = [];
  
  loading = false;
  error = '';
  success = '';
  showAddForm = false;
  editingUser: User | null = null;
  selectedUser: User | null = null;

  userForm: FormGroup;

  constructor(
    private userManagementService: UserManagementService,
    private departmentService: DepartmentService,
    private formBuilder: FormBuilder,
    private router: Router
  ) {
    this.userForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      lastName: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(50)]],
      email: ['', [Validators.required, Validators.email, Validators.maxLength(100)]],
      role: ['', [Validators.required]],
      departmentId: ['', [Validators.required]],
      // supervisorId: [''],
      isActive: [true]
    });
  }

  ngOnInit(): void {
    this.loadInitialData();
  }


  loadInitialData(): void {
    this.loading = true;
    this.error = '';

    // Test with just users first to isolate the issue
    console.log('Starting to load users...');
    this.userManagementService.getAllUsers().subscribe({
      next: (users) => {
        console.log('Users loaded successfully:', users);
        this.users = users.users;
        this.loadOtherData(); // Load other data after users succeed
      },
      error: (error) => {
        console.error('Failed to load users:', error);
        this.error = error.message || 'Failed to load users';
        this.loading = false;
      }
    });
  }

  loadOtherData(): void {
    // Load other data separately
    forkJoin({
      departments: this.departmentService.getAllDepartments(),
      roles: this.userManagementService.getRoles(),
      // supervisors: this.userManagementService.getSupervisors()
    }).subscribe({
      next: (data) => {
        this.departments = data.departments;
        this.roles = data.roles;
        // this.supervisors = data.supervisors.map((supervisor: Supervisor) => ({
        //   ...supervisor,
        //   displayName: `${supervisor.firstName} ${supervisor.lastName} (${supervisor.role})`
        // }));
        this.loading = false;

        console.log("this.roles", data.roles);
        console.log("this.supervisors", this.supervisors);
        console.log('All data loaded successfully');
      },
      error: (error) => {
        console.error('Failed to load other data:', error);
        // Don't show error for other data, users are more important
        this.loading = false;
      }
    });
  }

  showAddUserForm(): void {
    this.showAddForm = true;
    this.editingUser = null;
    this.userForm.reset();
    this.userForm.patchValue({ isActive: true });
    this.error = '';
    this.success = '';
  }

  editUser(user: User): void {
    this.editingUser = user;
    this.showAddForm = true;
    
    // Handle role - it might be an object or string
    const roleValue = typeof user.role === 'object' ? user.role.name : user.role;
    
    this.userForm.patchValue({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: roleValue,
      departmentId: user.department?.id || user.departmentId,
      // supervisorId: user.supervisor?.id || user.supervisorId,
      isActive: user.isActive !== false
    });
    this.error = '';
    this.success = '';
  }

  cancelForm(): void {
    this.showAddForm = false;
    this.editingUser = null;
    this.userForm.reset();
    this.error = '';
    this.success = '';
  }

  onSubmit(): void {
    if (this.userForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    const formData = this.userForm.value;
    this.loading = true;
    this.error = '';

    if (this.editingUser) {
      // Update existing user
      const updateData: UpdateUserRequest = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        role: formData.role,
        departmentId: String(formData.departmentId),
        // supervisorId: formData.supervisorId || undefined,
        isActive: formData.isActive
      };

      this.userManagementService.updateUser(this.editingUser.id!, updateData).subscribe({
        next: (updatedUser) => {
          this.success = 'User updated successfully';
          this.loadInitialData();
          this.cancelForm();
        },
        error: (error) => {
          this.error = error.message || 'Failed to update user';
          this.loading = false;
        }
      });
    } else {
      // Create new user
      const createData: CreateUserRequest = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        role: formData.role,
        departmentId: String(formData.departmentId),
        // supervisorId: formData.supervisorId || undefined
      };

      this.userManagementService.createUser(createData).subscribe({
        next: (response: CreateUserResponse) => {
          this.success = response.message || 'User created successfully';
          this.loadInitialData();
          this.cancelForm();
        },
        error: (error) => {
          this.error = error.message || 'Failed to create user';
          this.loading = false;
        }
      });
    }
  }

  deleteUser(user: User): void {
    const userName = `${user.firstName} ${user.lastName}`;
    if (confirm(`Are you sure you want to delete user "${userName}"? This action cannot be undone.`)) {
      this.loading = true;
      this.error = '';

      this.userManagementService.deleteUser(user.id!).subscribe({
        next: () => {
          this.success = 'User deleted successfully';
          this.loadInitialData();
        },
        error: (error) => {
          this.error = error.message || 'Failed to delete user';
          this.loading = false;
        }
      });
    }
  }

  toggleUserStatus(user: User): void {
    const newStatus = !user.isActive;
    const action = newStatus ? 'activate' : 'deactivate';
    const userName = `${user.firstName} ${user.lastName}`;
    
    if (confirm(`Are you sure you want to ${action} user "${userName}"?`)) {
      this.loading = true;
      this.error = '';

      this.userManagementService.toggleUserStatus(user.id!, newStatus).subscribe({
        next: (updatedUser) => {
          this.success = `User ${action}d successfully`;
          this.loadInitialData();
        },
        error: (error) => {
          this.error = error.message || `Failed to ${action} user`;
          this.loading = false;
        }
      });
    }
  }

  resetPassword(user: User): void {
    const userName = `${user.firstName} ${user.lastName}`;
    if (confirm(`Reset password for "${userName}"? A temporary password will be sent to their email.`)) {
      this.loading = true;
      this.error = '';

      this.userManagementService.resetPassword(user.id!).subscribe({
        next: (response) => {
          this.success = response.message || 'Password reset successfully';
          this.loading = false;
        },
        error: (error) => {
          this.error = error.message || 'Failed to reset password';
          this.loading = false;
        }
      });
    }
  }

  goBack(): void {
    this.router.navigate(['/admin']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach(key => {
      this.userForm.get(key)?.markAsTouched();
    });
  }

  get f() { 
    return this.userForm.controls; 
  }

  getUserRoleName(user: User): string {
    return typeof user.role === 'object' ? user.role.name : user.role;
  }

  getUserDepartmentName(user: User): string {
    return user.department?.name || 'N/A';
  }

  // getUserSupervisorName(user: User): string {
  //   if (user.supervisor) {
  //     return `${user.supervisor.firstName} ${user.supervisor.lastName}`;
  //   }
  //   return 'N/A';
  // }

  getActiveUsersCount(): number {
    console.log("this.users", this.users)
    return this.users.filter(u => u.isActive !== false).length;
  }

  getInactiveUsersCount(): number {
    return this.users.filter(u => u.isActive === false).length;
  }

  getUsersPlural(): string {
    return this.users.length !== 1 ? 's' : '';
  }

  getUserStatusBadgeClass(user: User): string {
    return user.isActive !== false ? 'bg-success' : 'bg-secondary';
  }

  getUserStatusText(user: User): string {
    return user.isActive !== false ? 'Active' : 'Inactive';
  }

  getToggleButtonClass(user: User): string {
    return user.isActive !== false ? 'btn-outline-warning' : 'btn-outline-success';
  }

  getToggleButtonTitle(user: User): string {
    return user.isActive !== false ? 'Deactivate User' : 'Activate User';
  }

  getToggleButtonIcon(user: User): string {
    return user.isActive !== false ? 'fa-user-slash' : 'fa-user-check';
  }

  getToggleButtonText(user: User): string {
    return user.isActive !== false ? 'Deactivate' : 'Activate';
  }

  getToggleButtonDescription(user: User): string {
    return user.isActive !== false
      ? 'Temporarily disable user access'
      : 'Restore user access to the system';
  }

  isUsersEmpty(): boolean {
    return this.users.length === 0;
  }

  // Modal methods
  openUserActionsModal(user: User): void {
    this.selectedUser = user;
    const modalElement = document.getElementById('userActionsModal');
    if (modalElement) {
      const modal = new (window as any).bootstrap.Modal(modalElement);
      modal.show();
    }
  }

  closeModal(): void {
    const modalElement = document.getElementById('userActionsModal');
    if (modalElement) {
      const modal = (window as any).bootstrap.Modal.getInstance(modalElement);
      if (modal) {
        modal.hide();
      }
    }
    this.selectedUser = null;
  }
}
