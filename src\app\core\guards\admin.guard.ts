import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    _route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    const isAuthenticated = this.authService.isAuthenticated();
    const isEmployee = this.authService.isEmployee();
    const currentUser = this.authService.getCurrentUser();

    console.log('AdminGuard - Access attempt to:', state.url);
    console.log('AdminGuard - Is authenticated:', isAuthenticated);
    console.log('AdminGuard - Is employee:', isEmployee);
    console.log('AdminGuard - Current user role:', currentUser?.role);

    if (isAuthenticated && !isEmployee) {
      // All roles except Employee can access admin routes
      console.log('AdminGuard - Access granted to admin user');
      return true;
    } else if (isAuthenticated) {
      // User is authenticated but is Employee - redirect to employee dashboard
      console.log('AdminGuard - User is employee, redirecting to employee dashboard');
      this.router.navigate(['/employee']);
      return false;
    } else {
      // User is not authenticated
      console.log('AdminGuard - User not authenticated, redirecting to admin login');
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return false;
    }
  }
}
