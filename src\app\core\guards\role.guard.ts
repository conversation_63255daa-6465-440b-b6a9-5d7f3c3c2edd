import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class RoleGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login'], { queryParams: { returnUrl: state.url } });
      return false;
    }

    // Get required roles from route data
    const requiredRoles = route.data['roles'] as string[];
    
    if (!requiredRoles || requiredRoles.length === 0) {
      // No specific roles required, just need to be authenticated
      return true;
    }

    // Check if user has any of the required roles
    if (this.authService.hasAnyRole(requiredRoles)) {
      return true;
    }

    // User doesn't have required role - redirect to appropriate page
    if (this.authService.isEmployee()) {
      this.router.navigate(['/employee']);
    } else {
      this.router.navigate(['/admin']);
    }
    
    return false;
  }
}
