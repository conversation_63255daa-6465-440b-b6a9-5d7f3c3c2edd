import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { AuthService } from './auth.service';

export interface QuestionOption {
  id: string;
  option_text: string;
  option_value: number;
  sort_order: number;
}

export interface Question {
  id: string;
  question_text: string;
  question_type: string;
  sort_order: number;
  isActive: boolean;
  options: QuestionOption[];
}

export interface QuestionsResponse {
  questions: Question[];
  total: number;
}

export interface SurveyResponseItem {
  questionId: string;
  selectedOptionId: string;
}

export interface SurveySubmissionRequest {
  userId: string;
  companyId: string;
  responses: SurveyResponseItem[];
  department: string;
}

export interface SurveySubmissionResponse {
  success: boolean;
  message: string;
  submissionId?: string;
}

@Injectable({
  providedIn: 'root'
})
export class QuestionsService {
  private readonly API_URL = 'http://localhost:3000/api';

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getToken();
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred!';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      if (error.error && error.error.message) {
        errorMessage = error.error.message;
      }
    }
    
    console.error('Questions Service Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  getAllQuestions(): Observable<QuestionsResponse> {
    const headers = this.getAuthHeaders();
    return this.http.get<QuestionsResponse>(`${this.API_URL}/questions`, { headers })
      .pipe(catchError(this.handleError));
  }

  getQuestionById(id: string): Observable<Question> {
    const headers = this.getAuthHeaders();
    return this.http.get<Question>(`${this.API_URL}/questions/${id}`, { headers })
      .pipe(catchError(this.handleError));
  }

  createQuestion(question: Partial<Question>): Observable<Question> {
    const headers = this.getAuthHeaders();
    return this.http.post<Question>(`${this.API_URL}/questions`, question, { headers })
      .pipe(catchError(this.handleError));
  }

  updateQuestion(id: string, question: Partial<Question>): Observable<Question> {
    const headers = this.getAuthHeaders();
    return this.http.put<Question>(`${this.API_URL}/questions/${id}`, question, { headers })
      .pipe(catchError(this.handleError));
  }

  deleteQuestion(id: string): Observable<void> {
    const headers = this.getAuthHeaders();
    return this.http.delete<void>(`${this.API_URL}/questions/${id}`, { headers })
      .pipe(catchError(this.handleError));
  }

  toggleQuestionStatus(id: string, isActive: boolean): Observable<Question> {
    const headers = this.getAuthHeaders();
    return this.http.patch<Question>(`${this.API_URL}/questions/${id}/status`,
      { isActive },
      { headers }
    ).pipe(catchError(this.handleError));
  }

  submitSurvey(surveyData: SurveySubmissionRequest): Observable<SurveySubmissionResponse> {
    const headers = this.getAuthHeaders();
    return this.http.post<SurveySubmissionResponse>(`${this.API_URL}/survey/submit`, surveyData, { headers })
      .pipe(catchError(this.handleError));
  }
}
