/* ===== DEPARTMENT MANAGEMENT STYLES ===== */

/* Header Section */
.header-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-title h1 {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1.75rem;
}

.back-btn, .add-btn {
  border-radius: 0.5rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.add-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* Alerts Container */
.alerts-container {
  margin-bottom: 1.5rem;
}

.alert {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border-left: 4px solid #28a745;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left: 4px solid #dc3545;
}

/* Form Card */
.form-card .card {
  border-radius: 0.75rem;
  overflow: hidden;
}

.form-card .card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
  padding: 1.25rem 1.5rem;
}

.form-card .card-body {
  background-color: #fff;
}

/* Form Styles */
.form-label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-control {
  border: 2px solid #e9ecef;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background-color: #fff;
}

.form-control-lg {
  padding: 1rem 1.25rem;
  font-size: 1rem;
}

.form-text {
  margin-top: 0.5rem;
}

.form-text i {
  color: #6c757d;
}

.form-control:focus {
  border-color: #007bff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
  background-color: #fff;
}

.form-control.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #dc3545;
  font-weight: 500;
}

.form-actions {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
}

/* Stats Cards */
.stats-card {
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.stats-icon {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* Departments Table Card */
.departments-table-card {
  border-radius: 0.75rem;
  overflow: hidden;
}

.departments-table-card .card-header {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  padding: 1.25rem 1.5rem;
}

/* Table Styles */
.table {
  margin-bottom: 0;
  font-size: 0.95rem;
}

.table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  color: #495057;
  font-weight: 600;
  padding: 1rem 1.25rem;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tbody td {
  padding: 1.25rem;
  vertical-align: middle;
  border-bottom: 1px solid #f1f3f4;
}

.department-row {
  transition: all 0.2s ease;
}

.department-row:hover {
  background-color: #f8f9fa;
  transform: scale(1.001);
}

.department-name strong {
  font-size: 1rem;
  color: #2c3e50;
}

.department-description {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.action-buttons .btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border-width: 2px;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Empty State */
.empty-state {
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0.75rem;
  margin: 2rem 0;
}

.empty-icon {
  margin-bottom: 1.5rem;
}

.empty-state h4 {
  color: #6c757d;
  font-weight: 600;
}

.empty-state p {
  color: #6c757d;
  font-size: 1.1rem;
  max-width: 400px;
  margin: 0 auto;
}

/* Button Enhancements */
.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  border: none;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
  background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4);
}

.btn-outline-primary:hover {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  transform: translateY(-1px);
}

.btn-outline-danger:hover {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  transform: translateY(-1px);
}

.btn-outline-secondary:hover {
  background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
  transform: translateY(-1px);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles */
@media (max-width: 992px) {
  .header-section .d-flex {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .page-title {
    order: -1;
    width: 100%;
    text-align: center;
  }

  .page-title h1 {
    font-size: 1.5rem;
  }

  .stats-card {
    margin-bottom: 1rem;
  }

  .table thead th {
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
  }

  .table tbody td {
    padding: 1rem;
  }

  .action-buttons .btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
}

/* Mobile Styles */
@media (max-width: 768px) {
  .header-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .page-title h1 {
    font-size: 1.25rem;
  }

  .back-btn, .add-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .form-card .card-body {
    padding: 1.5rem;
  }

  .stats-card .card-body {
    padding: 1.5rem;
  }

  .stats-icon {
    width: 50px;
    height: 50px;
  }

  .stats-icon i {
    font-size: 1.5rem;
  }

  .departments-table-card .card-header {
    padding: 1rem;
  }

  .table thead th {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }

  .table tbody td {
    padding: 0.75rem 0.5rem;
  }

  .department-description {
    max-width: 200px;
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    width: 100%;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-state h4 {
    font-size: 1.1rem;
  }

  .empty-state p {
    font-size: 0.95rem;
  }
}

/* Small Mobile Styles */
@media (max-width: 576px) {
  .header-section .d-flex {
    flex-direction: column;
    text-align: center;
  }

  .back-btn, .add-btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .form-card .card-body {
    padding: 1rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .stats-card .card-body {
    padding: 1rem;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .department-name strong {
    font-size: 0.9rem;
  }

  .department-description {
    max-width: 150px;
    font-size: 0.8rem;
  }
}

/* ===== LOADING AND ANIMATIONS ===== */

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Loading Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.departments-content {
  animation: fadeIn 0.5s ease-in-out;
}

.form-card {
  animation: fadeIn 0.3s ease-in-out;
}

/* Icon Styles */
.fas {
  transition: all 0.3s ease;
}

.btn:hover .fas {
  transform: scale(1.1);
}

/* Hover Effects */
.stats-card:hover .stats-icon {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.stats-card:hover .stats-icon i {
  transform: rotate(5deg);
  transition: transform 0.3s ease;
}

/* Focus States */
.btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control:focus {
  transform: translateY(-1px);
}

/* Custom Scrollbar */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Print Styles */
@media print {
  .header-section,
  .form-card,
  .action-buttons,
  .btn {
    display: none !important;
  }

  .departments-table-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .table {
    font-size: 12px !important;
  }
}
