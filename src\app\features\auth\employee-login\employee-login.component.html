<div class="container-fluid p-0">
  <div class="row m-0">
    <div class="col-12 p-0">
      <div class="login-card">
        <div class="login-main">
          <div class="text-center">
            <a class="logo" href="#">VE</a>
          </div>

          <app-loading *ngIf="loading" message="Signing you in..."></app-loading>

          <form (ngSubmit)="onSubmit()" #loginForm="ngForm" class="theme-form" *ngIf="!loading">
            <h1 class="text-center">EMPLOYEE LOGIN</h1>

            <div class="alert alert-danger" *ngIf="errorMessage">
              {{ errorMessage }}
            </div>

            <div class="form-group">
              <label class="col-form-label">Email Address</label>
              <input
                class="form-control"
                type="email"
                name="email"
                [(ngModel)]="loginData.email"
                placeholder="<EMAIL>"
                required
                email
                #email="ngModel"
                [class.is-invalid]="email.invalid && email.touched">
              <div class="invalid-feedback" *ngIf="email.invalid && email.touched">
                <div *ngIf="email.errors?.['required']">Email is required</div>
                <div *ngIf="email.errors?.['email']">Please enter a valid email</div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-form-label">Password</label>
              <div class="form-input position-relative">
                <input
                  class="form-control"
                  [type]="showPassword ? 'text' : 'password'"
                  name="password"
                  [(ngModel)]="loginData.password"
                  placeholder="*********"
                  required
                  minlength="6"
                  #password="ngModel"
                  [class.is-invalid]="password.invalid && password.touched">
                <button
                  type="button"
                  class="btn btn-link position-absolute end-0 top-50 translate-middle-y me-2"
                  (click)="togglePasswordVisibility()"
                  style="z-index: 10; border: none; background: none;">
                  <i [class]="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'" class="text-muted"></i>
                </button>
                <div class="invalid-feedback" *ngIf="password.invalid && password.touched">
                  <div *ngIf="password.errors?.['required']">Password is required</div>
                  <div *ngIf="password.errors?.['minlength']">Password must be at least 6 characters</div>
                </div>
              </div>
            </div>

            <div class="form-group mb-0">
              <div class="text-end">
                <button
                  class="btn btn-primary btn-block w-100 mt-3"
                  type="submit"
                  [disabled]="loginForm.invalid || loading">
                  <span *ngIf="loading" class="spinner-border spinner-border-sm me-2" role="status"></span>
                  {{ loading ? 'Signing In...' : 'Sign In as Employee' }}
                </button>
              </div>
            </div>

            <!-- Demo Login Button -->
            <div class="form-group mb-0 mt-2">
              <button
                type="button"
                class="btn btn-outline-info w-100"
                (click)="demoEmployeeLogin()"
                [disabled]="loading">
                <i class="fas fa-user-check me-2"></i>
                Demo Employee Login
              </button>
            </div>
          </form>

          <!-- Admin Login Link -->
          <div class="text-center mt-4" *ngIf="!loading">
            <hr class="my-3">
            <p class="text-muted mb-2">Need admin access?</p>
            <a
              routerLink="/login"
              class="btn btn-outline-warning btn-sm">
              <i class="fas fa-user-shield me-2"></i>
              Admin Login
            </a>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>
