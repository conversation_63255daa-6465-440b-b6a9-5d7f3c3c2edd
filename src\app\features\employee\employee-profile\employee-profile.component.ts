import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AuthService, User } from '../../../core/services/auth.service';
import { EmployeeService, Employee } from '../../../core/services/employee.service';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

@Component({
  selector: 'app-employee-profile',
  standalone: true,
  imports: [CommonModule, LoadingComponent],
  templateUrl: './employee-profile.component.html',
  styleUrls: ['./employee-profile.component.css']
})
export class EmployeeProfileComponent implements OnInit {
  currentUser: User | null = null;
  employeeDetails: Employee | null = null;
  loading = false;

  constructor(
    private authService: AuthService,
    private employeeService: EmployeeService,
    public router: Router
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
    if (this.currentUser) {
      this.loadEmployeeDetails();
    }
  }

  loadEmployeeDetails(): void {
    if (!this.currentUser) return;
    
    this.loading = true;
    this.employeeService.getAllEmployees().subscribe({
      next: (employees) => {
        this.employeeDetails = employees.find(emp => emp.email === this.currentUser?.email) || null;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading employee details:', error);
        this.loading = false;
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/employee']);
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  get userDisplayName(): string {
    if (this.currentUser) {
      return `${this.currentUser.firstName} ${this.currentUser.lastName}`.trim();
    }
    return '';
  }
}
