import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { TranslatePipe } from '../../../shared/pipes/translate.pipe';
import { ApiTestService } from '../../../core/services/api-test.service';
import { TranslationService } from '../../../core/services/translation.service';
import { DataRefreshService } from '../../../core/services/data-refresh.service';

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [CommonModule, TranslatePipe],
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.css']
})
export class AdminDashboardComponent {
  lastApiResponse: any = null;

  constructor(
    private router: Router,
    public authService: AuthService,
    private apiTestService: ApiTestService,
    private translationService: TranslationService,
    private dataRefreshService: DataRefreshService
  ) {}

  navigateToReports(): void {
    this.router.navigate(['/admin/reports']);
  }

  navigateToProfile(): void {
    this.router.navigate(['/admin/profile']);
  }

  navigateToDepartments(): void {
    this.router.navigate(['/admin/departments']);
  }

  navigateToUserManagement(): void {
    this.router.navigate(['/admin/users']);
  }

  // Language testing methods
  // getCurrentLanguage(): string {
  //   return this.translationService.getCurrentLanguage();
  // }

  // getCurrentLanguageInfo() {
  //   return this.translationService.getCurrentLanguageInfo();
  // }

  // testApiHeaders(): void {
  //   console.log('🧪 Testing API headers with current language:', this.getCurrentLanguage());

  //   this.apiTestService.testApiCall().subscribe({
  //     next: (response) => {
  //       console.log('✅ API Test Response:', response);
  //       this.lastApiResponse = response;
  //     },
  //     error: (error) => {
  //       console.error('❌ API Test Error:', error);
  //       this.lastApiResponse = { error: error.message };
  //     }
  //   });
  // }

  // testPostRequest(): void {
  //   const testData = {
  //     title: 'Test Post with Language Header',
  //     body: `Testing POST request with language: ${this.getCurrentLanguage()}`,
  //     userId: 1,
  //     language: this.getCurrentLanguage()
  //   };

  //   console.log('🧪 Testing POST request with data:', testData);

  //   this.apiTestService.testPostRequest(testData).subscribe({
  //     next: (response) => {
  //       console.log('✅ POST Test Response:', response);
  //       this.lastApiResponse = response;
  //     },
  //     error: (error) => {
  //       console.error('❌ POST Test Error:', error);
  //       this.lastApiResponse = { error: error.message };
  //     }
  //   });
  // }

  // testDataRefresh(): void {
  //   console.log('🧪 Testing manual data refresh for all registered components');
  //   console.log('📋 Registered components:', this.dataRefreshService.getRegisteredComponents());
  //   this.dataRefreshService.refreshAllComponents();
  // }
}
