<div class="text-center">
  <h1><strong>{{ employeeName }}, yesterday you did a great Job</strong></h1>
</div>

<app-loading *ngIf="loading" [message]="'survey.surveyQuestions' | translate"></app-loading>
<app-loading *ngIf="submitting" [message]="'survey.submitSurvey' | translate"></app-loading>

  <!-- Questions -->
  <div class="mt-4 text-center" *ngIf="!loading && !submitting && !showCompletion && questions.length > 0">
    <div class="question-box">
      <h3>{{ questions[currentQuestionIndex].question_text }}</h3>
      <div class="d-flex flex-wrap flex-row gap-4">
        <div
          *ngFor="let option of questions[currentQuestionIndex].options"
          class="graybox text-center"
          [class.selected]="isAnswerSelected(questions[currentQuestionIndex].id, option.option_text)"
          (click)="onAnswerSelect(questions[currentQuestionIndex].id, option.option_text)">
          <a href="#" (click)="$event.preventDefault()">{{ option.option_text }}</a>
        </div>
      </div>
    </div>

    <!-- Navigation buttons -->
    <div class="d-flex justify-content-between mt-4">
      <button 
        class="btn btn-outline-secondary"
        (click)="previousQuestion()"
        [disabled]="currentQuestionIndex === 0">
        {{ 'common.previous' | translate }}
      </button>
      
      <span class="align-self-center">
        {{ 'survey.question' | translate }} {{ currentQuestionIndex + 1 }} {{ 'common.of' | translate }} {{ questions.length }}
      </span>
      
      <button 
        class="btn btn-primary"
        (click)="nextQuestion()"
        [disabled]="!canProceed()">
        {{ currentQuestionIndex === questions.length - 1 ? ('common.submit' | translate) : ('common.next' | translate) }}
      </button>
    </div>
  </div>

  <!-- Completion message -->
  <div class="mt-4 text-center " *ngIf="showCompletion">
    <div class="greatjob-box">
      <img src="assets/images/thumb.svg" alt="Great Job">
      <h3>{{ 'survey.thankYouForParticipating' | translate }}</h3>
    </div>

    <div>

      <button class="btn btn-primary mt-4" (click)="goHome()">
        Back to Home
      </button>
    </div>
    
  </div>

  <!-- No questions message -->
  <div class="text-center mt-5" *ngIf="!loading && questions.length === 0">
    <p class="text-muted">No questions available at this time.</p>
    <button class="btn btn-outline-secondary" (click)="goHome()">
      Back to Home
    </button>
  </div>
