<header>
  <div class="company-logo">
    ABC LTD
  </div>

  <div class="d-flex align-items-center gap-3">
    <!-- Language Selector -->
    <app-language-selector></app-language-selector>

    <div class="dropdown topmenu">
      <a class="dropdown-toggle d-inline-block" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
        <img src="assets/images/hamburger.svg" alt="Menu">
      </a>
      <ul class="dropdown-menu">
        <li><button class="dropdown-item" type="button" (click)="navigateToHome()">{{ 'navigation.home' | translate }}</button></li>
        <li><button class="dropdown-item" type="button" (click)="navigateToProfile()">{{ 'navigation.profile' | translate }}</button></li>
        <li><hr class="dropdown-divider"></li>
        <li><button class="dropdown-item" type="button" (click)="logout()">{{ 'auth.logout' | translate }}</button></li>
      </ul>
    </div>

    <!-- <div class="user-info me-3" *ngIf="currentUser">
      <span class="text-light">{{ userDisplayName }}</span>
    </div> -->
    <a class="logo" [routerLink]="authService.isEmployee() ? '/employee' : '/admin'">VE</a>
  </div>
</header>
