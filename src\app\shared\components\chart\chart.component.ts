import { Component, Input, OnInit, OnDestroy, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';

Chart.register(...registerables);

@Component({
  selector: 'app-chart',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="chart-container" [style.height]="height">
      <canvas #chartCanvas></canvas>
    </div>
  `,
  styles: [`
    .chart-container {
      position: relative;
      width: 100%;
    }
    canvas {
      max-width: 100%;
      height: auto !important;
    }
  `]
})
export class ChartComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('chartCanvas', { static: true }) chartCanvas!: ElementRef<HTMLCanvasElement>;
  @Input() type: ChartType = 'bar';
  @Input() data: any;
  @Input() options: any = {};
  @Input() height: string = '400px';
  @Input() responsive: boolean = true;

  private chart: Chart | null = null;

  ngOnInit(): void {
    // Component initialization
  }

  ngAfterViewInit(): void {
    this.createChart();
  }

  ngOnDestroy(): void {
    if (this.chart) {
      this.chart.destroy();
    }
  }

  private createChart(): void {
    if (!this.chartCanvas || !this.data) {
      return;
    }

    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) {
      return;
    }

    // Default options
    const defaultOptions = {
      responsive: this.responsive,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        tooltip: {
          enabled: true,
        }
      }
    };

    // Merge with custom options
    const mergedOptions = { ...defaultOptions, ...this.options };

    const config: ChartConfiguration = {
      type: this.type,
      data: this.data,
      options: mergedOptions
    };

    this.chart = new Chart(ctx, config);
  }

  updateChart(newData: any): void {
    if (this.chart) {
      this.chart.data = newData;
      this.chart.update();
    }
  }

  updateOptions(newOptions: any): void {
    if (this.chart) {
      this.chart.options = { ...this.chart.options, ...newOptions };
      this.chart.update();
    }
  }
}
