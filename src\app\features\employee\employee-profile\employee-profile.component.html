<div class="d-flex justify-content-between align-items-center mb-4">
  <button class="btn btn-outline-secondary" (click)="goBack()">
    ← Back to Home
  </button>
  <h1 class="mb-0">My Profile</h1>
  <button class="btn btn-outline-danger" (click)="logout()">
    Logout
  </button>
</div>

  <app-loading *ngIf="loading" message="Loading profile..."></app-loading>

  <div class="profile-wrapper" *ngIf="!loading && currentUser">
    <div class="card">
      <div class="card-header text-center">
        <img
          *ngIf="employeeDetails?.avatar"
          [src]="employeeDetails?.avatar"
          [alt]="userDisplayName"
          class="profile-avatar mb-3">
        <h3>{{ userDisplayName }}</h3>
        <p class="text-muted">{{ currentUser.role | titlecase }}</p>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>Email:</strong></label>
            <p class="form-control-plaintext">{{ currentUser.email }}</p>
          </div>
          <div class="col-md-6 mb-3" *ngIf="employeeDetails">
            <label class="form-label"><strong>Department:</strong></label>
            <p class="form-control-plaintext">{{ employeeDetails.department }}</p>
          </div>
          <div class="col-md-6 mb-3" *ngIf="employeeDetails">
            <label class="form-label"><strong>Position:</strong></label>
            <p class="form-control-plaintext">{{ employeeDetails.position }}</p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>User ID:</strong></label>
            <p class="form-control-plaintext">#{{ currentUser.id }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0">Quick Actions</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-3">
            <button class="btn btn-primary w-100" (click)="router.navigate(['/employee'])">
              Take Survey
            </button>
          </div>
          <div class="col-md-6 mb-3">
            <button class="btn btn-outline-primary w-100" (click)="router.navigate(['/employee/list'])">
              View Employees
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="text-center mt-5" *ngIf="!loading && !currentUser">
    <p class="text-muted">Unable to load profile information.</p>
    <button class="btn btn-primary" (click)="router.navigate(['/login'])">
      Go to Login
    </button>
  </div>
