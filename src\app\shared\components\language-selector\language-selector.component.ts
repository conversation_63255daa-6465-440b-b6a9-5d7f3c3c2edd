import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { TranslationService, Language } from '../../../core/services/translation.service';
import { TranslatePipe } from '../../pipes/translate.pipe';
import { ClickOutsideDirective } from '../../directives/click-outside.directive';

@Component({
  selector: 'app-language-selector',
  standalone: true,
  imports: [CommonModule, TranslatePipe, ClickOutsideDirective],
  templateUrl: './language-selector.component.html',
  styleUrls: ['./language-selector.component.css']
})
export class LanguageSelectorComponent implements OnInit, OnDestroy {
  currentLanguage: Language | undefined;
  supportedLanguages: Language[] = [];
  isDropdownOpen = false;
  private subscription?: Subscription;

  constructor(private translationService: TranslationService) {}

  ngOnInit(): void {
    this.supportedLanguages = this.translationService.supportedLanguages;
    
    this.subscription = this.translationService.currentLanguage$.subscribe(langCode => {
      this.currentLanguage = this.supportedLanguages.find(lang => lang.code === langCode);
    });
  }

  ngOnDestroy(): void {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  async selectLanguage(language: Language): Promise<void> {
    await this.translationService.setLanguage(language.code);
    this.isDropdownOpen = false;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }
}
