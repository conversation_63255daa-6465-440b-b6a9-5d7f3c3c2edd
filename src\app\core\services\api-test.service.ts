import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TranslationService } from './translation.service';

@Injectable({
  providedIn: 'root'
})
export class ApiTestService {
  private readonly baseUrl = 'https://jsonplaceholder.typicode.com'; // Test API

  constructor(
    private http: HttpClient,
    private translationService: TranslationService
  ) {}

  // Test method to demonstrate language headers in API calls
  testApiCall(): Observable<any> {
    const currentLang = this.translationService.getCurrentLanguage();
    
    console.log(`🔄 ApiTestService: Making test API call with language: ${currentLang}`);
    
    // The LanguageInterceptor will automatically add language headers
    return this.http.get(`${this.baseUrl}/posts/1`);
  }

  // Test method with manual headers (to show both approaches)
  testApiCallWithManualHeaders(): Observable<any> {
    const currentLang = this.translationService.getCurrentLanguage();
    
    const headers = new HttpHeaders({
      'Accept-Language': currentLang,
      'X-Custom-Language': currentLang,
      'Content-Type': 'application/json'
    });

    console.log(`🔄 ApiTestService: Making test API call with manual headers:`, {
      'Accept-Language': currentLang,
      'X-Custom-Language': currentLang
    });

    return this.http.get(`${this.baseUrl}/posts/2`, { headers });
  }

  // Simulate a POST request with language headers
  testPostRequest(data: any): Observable<any> {
    const currentLang = this.translationService.getCurrentLanguage();
    
    console.log(`📤 ApiTestService: Making POST request with language: ${currentLang}`, data);
    
    // The LanguageInterceptor will automatically add language headers
    return this.http.post(`${this.baseUrl}/posts`, data);
  }

  // Get current language for display
  getCurrentLanguage(): string {
    return this.translationService.getCurrentLanguage();
  }

  // Get language info for display
  getCurrentLanguageInfo() {
    return this.translationService.getCurrentLanguageInfo();
  }
}
