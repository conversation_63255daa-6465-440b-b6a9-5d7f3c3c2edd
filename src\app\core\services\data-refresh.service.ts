import { Injectable, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Subscription } from 'rxjs';
import { TranslationService } from './translation.service';

export interface RefreshableComponent {
  refreshData(): void;
  componentName?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DataRefreshService implements OnDestroy {
  private registeredComponents: Map<string, RefreshableComponent> = new Map();
  private languageChangeSubscription?: Subscription;

  constructor(private translationService: TranslationService) {
    this.initializeLanguageChangeListener();
  }

  private initializeLanguageChangeListener(): void {
    this.languageChangeSubscription = this.translationService.languageChange$.subscribe(
      (newLanguage: string) => {
        console.log(`🔄 DataRefreshService: Language changed to ${newLanguage}, refreshing registered components`);
        this.refreshAllComponents(newLanguage);
      }
    );
  }

  // Register a component for automatic refresh on language change
  registerComponent(componentId: string, component: RefreshableComponent): void {
    console.log(`📝 DataRefreshService: Registering component ${componentId}`, component.componentName || 'Unknown');
    this.registeredComponents.set(componentId, component);
  }

  // Unregister a component (call this in ngOnDestroy)
  unregisterComponent(componentId: string): void {
    console.log(`🗑️ DataRefreshService: Unregistering component ${componentId}`);
    this.registeredComponents.delete(componentId);
  }

  // Manually trigger refresh for all registered components
  refreshAllComponents(language?: string): void {
    const currentLanguage = language || this.translationService.getCurrentLanguage();
    console.log(`🔄 DataRefreshService: Refreshing ${this.registeredComponents.size} components for language: ${currentLanguage}`);

    this.registeredComponents.forEach((component, componentId) => {
      try {
        console.log(`🔄 Refreshing component: ${componentId} (${component.componentName || 'Unknown'})`);
        component.refreshData();
      } catch (error) {
        console.error(`❌ Error refreshing component ${componentId}:`, error);
      }
    });
  }

  // Manually trigger refresh for a specific component
  refreshComponent(componentId: string): void {
    const component = this.registeredComponents.get(componentId);
    if (component) {
      console.log(`🔄 DataRefreshService: Refreshing specific component: ${componentId}`);
      component.refreshData();
    } else {
      console.warn(`⚠️ DataRefreshService: Component ${componentId} not found for refresh`);
    }
  }

  // Get list of registered components (for debugging)
  getRegisteredComponents(): string[] {
    return Array.from(this.registeredComponents.keys());
  }

  // Check if a component is registered
  isComponentRegistered(componentId: string): boolean {
    return this.registeredComponents.has(componentId);
  }

  ngOnDestroy(): void {
    if (this.languageChangeSubscription) {
      this.languageChangeSubscription.unsubscribe();
    }
    this.registeredComponents.clear();
  }
}
