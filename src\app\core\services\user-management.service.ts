import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

export interface Role {
  id: string;
  name: string;
}

export interface Department {
  id: string;
  name: string;
}

export interface Supervisor {
  id: string;
  firstName: string;
  lastName: string;
  role: string;
}

export interface PaginatedUsers {
  users: User[];
  total: number;
  page: number;
  limit: number;
}


export interface User {
  id?: string;
  firstName: string;
  lastName: string;
  email: string;
  role: Role | string;
  department?: Department;
  supervisor?: Supervisor;
  departmentId?: string;
  supervisorId?: string;
  isActive?: boolean;
  isTemporaryPassword?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  departmentId: string;
  supervisorId?: string;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  role?: string;
  departmentId?: string;
  supervisorId?: string;
  isActive?: boolean;
}

export interface CreateUserResponse {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: Role;
  department: Department;
  supervisor?: Supervisor;
  isActive: boolean;
  isTemporaryPassword: boolean;
  createdAt: string;
  message: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserManagementService {
  private readonly API_URL = 'http://localhost:3000/api';

  constructor(private http: HttpClient) {}

  // Get all users
  getAllUsers(): Observable<PaginatedUsers> {
    return this.http.get<PaginatedUsers>(`${this.API_URL}/users`)
      .pipe(
        catchError(this.handleError),
      );
  }

  // Get user by ID
  getUserById(id: string): Observable<User> {
    return this.http.get<User>(`${this.API_URL}/users/${id}`)
      .pipe(catchError(this.handleError));
  }

  // Create new user
  createUser(user: CreateUserRequest): Observable<CreateUserResponse> {
    return this.http.post<CreateUserResponse>(`${this.API_URL}/users`, user)
      .pipe(catchError(this.handleError));
  }

  // Update user
  updateUser(id: string, user: UpdateUserRequest): Observable<User> {
    return this.http.put<User>(`${this.API_URL}/users/${id}`, user)
      .pipe(catchError(this.handleError));
  }

  // Delete user
  deleteUser(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/users/${id}`)
      .pipe(catchError(this.handleError));
  }

  // Get available roles
  getRoles(): Observable<Role[]> {
    return this.http.get<Role[]>(`${this.API_URL}/roles`)
      .pipe(
        map((res: any) => res.roles),
        catchError(this.handleError)
      );
  }

  // Get available supervisors
  getSupervisors(): Observable<Supervisor[]> {
    return this.http.get<Supervisor[]>(`${this.API_URL}/users/supervisors`)
      .pipe(catchError(this.handleError));
  }

  // Reset user password
  resetPassword(id: string): Observable<{message: string}> {
    return this.http.post<{message: string}>(`${this.API_URL}/users/${id}/reset-password`, {})
      .pipe(catchError(this.handleError));
  }

  // Toggle user active status
  toggleUserStatus(id: string, isActive: boolean): Observable<User> {
    return this.http.patch<User>(`${this.API_URL}/users/${id}/status`, { isActive })
      .pipe(catchError(this.handleError));
  }

  private handleError = (error: HttpErrorResponse) => {
    let errorMessage = 'An error occurred while processing the request';
    
    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.status === 400) {
      errorMessage = 'Invalid request data';
    } else if (error.status === 401) {
      errorMessage = 'Unauthorized access';
    } else if (error.status === 403) {
      errorMessage = 'Access forbidden';
    } else if (error.status === 404) {
      errorMessage = 'User not found';
    } else if (error.status === 409) {
      errorMessage = 'User with this email already exists';
    } else if (error.status === 422) {
      errorMessage = 'Validation failed. Please check your input.';
    } else if (error.status === 0) {
      errorMessage = 'Unable to connect to server. Please check your connection.';
    }
    
    return throwError(() => new Error(errorMessage));
  }
}
