{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "cli": {"packageManager": "npm", "analytics": false}, "newProjectRoot": "projects", "projects": {"ve-employee-survey": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/ve-employee-survey", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "public"}, "src/favicon.ico", "src/assets"], "styles": ["node_modules/@ng-select/ng-select/themes/default.theme.css", "src/styles.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"], "server": "src/main.server.ts", "prerender": true, "ssr": {"entry": "server.ts"}}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "4kB"}], "outputHashing": "none"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "outputHashing": "none"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "proxy.conf.json", "disableHostCheck": true}, "configurations": {"production": {"buildTarget": "ve-employee-survey:build:production"}, "development": {"buildTarget": "ve-employee-survey:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["node_modules/@ng-select/ng-select/themes/default.theme.css", "src/styles.css"], "scripts": ["node_modules/bootstrap/dist/js/bootstrap.bundle.min.js"]}}}}}}