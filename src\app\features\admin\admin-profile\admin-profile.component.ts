import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AuthService, User } from '../../../core/services/auth.service';
import { EmployeeService } from '../../../core/services/employee.service';

@Component({
  selector: 'app-admin-profile',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './admin-profile.component.html',
  styleUrls: ['./admin-profile.component.css']
})
export class AdminProfileComponent implements OnInit {
  currentUser: User | null = null;
  currentDate = new Date().toLocaleDateString();
  stats = {
    totalEmployees: 0,
    totalSurveys: 0,
    completionRate: 0
  };

  constructor(
    private authService: AuthService,
    private employeeService: EmployeeService,
    public router: Router
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
    this.loadStats();
  }

  loadStats(): void {
    this.employeeService.getAllEmployees().subscribe({
      next: (employees) => {
        this.stats.totalEmployees = employees.length;
        this.stats.totalSurveys = Math.floor(employees.length * 0.75); // Mock 75% completion
        this.stats.completionRate = this.stats.totalEmployees > 0 
          ? (this.stats.totalSurveys / this.stats.totalEmployees) * 100 
          : 0;
      },
      error: (error) => {
        console.error('Error loading stats:', error);
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/admin']);
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/login']);
  }

  get userDisplayName(): string {
    if (this.currentUser) {
      return `${this.currentUser.firstName} ${this.currentUser.lastName}`.trim();
    }
    return '';
  }
}
