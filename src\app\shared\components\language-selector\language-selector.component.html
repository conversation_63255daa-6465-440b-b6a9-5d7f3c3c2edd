<div class="language-selector" (clickOutside)="closeDropdown()">
  <button
    type="button"
    class="language-button"
    (click)="toggleDropdown()"
    [attr.aria-expanded]="isDropdownOpen"
    [attr.aria-label]="'common.selectLanguage' | translate"
    [title]="'common.selectLanguage' | translate">

    <span class="current-language">
      <i class="fas fa-globe language-icon"></i>
      <span class="language-code">{{ currentLanguage?.code?.toUpperCase() }}</span>
    </span>

    <i class="fas fa-chevron-down dropdown-icon"
       [class.rotated]="isDropdownOpen"></i>
  </button>

  <div class="language-dropdown" 
       [class.show]="isDropdownOpen"
       role="menu">
    
    <div class="dropdown-header">
      <i class="fas fa-globe me-2"></i>
      {{ 'common.selectLanguage' | translate }}
    </div>

    <div class="language-list">
      <button
        *ngFor="let language of supportedLanguages"
        type="button"
        class="language-option"
        [class.active]="currentLanguage?.code === language.code"
        (click)="selectLanguage(language)"
        role="menuitem">
        
        <span class="flag">{{ language.flag }}</span>
        <span class="language-info">
          <span class="language-name">{{ language.name }}</span>
          <span class="language-code-small">{{ language.code.toUpperCase() }}</span>
        </span>
        
        <i *ngIf="currentLanguage?.code === language.code" 
           class="fas fa-check check-icon"></i>
      </button>
    </div>

    <div class="dropdown-footer">
      <small class="text-muted">
        <i class="fas fa-info-circle me-1"></i>
        {{ 'common.languageChangeNote' | translate }}
      </small>
    </div>
  </div>
</div>
