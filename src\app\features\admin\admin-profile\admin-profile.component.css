.admin-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0099E3 0%, #017bb8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  font-size: 48px;
  color: white;
}

.card {
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background: linear-gradient(135deg, #0099E3 0%, #017bb8 100%);
  color: white;
  border-radius: 15px 15px 0 0 !important;
}

.form-control-plaintext {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 8px 12px;
  margin: 0;
}

.stat-card {
  padding: 1rem;
  border-radius: 10px;
  background-color: #f8f9fa;
  margin-bottom: 1rem;
}

.stat-card h3 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.text-primary { color: #0099E3 !important; }
.text-success { color: #28a745 !important; }
.text-info { color: #17a2b8 !important; }
