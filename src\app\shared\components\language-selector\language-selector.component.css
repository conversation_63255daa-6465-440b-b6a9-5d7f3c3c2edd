/* Language Selector Container */
.language-selector {
  position: relative;
  display: inline-block;
}

/* Integration with header */
.language-selector .language-button {
  height: 40px;
  display: flex;
  align-items: center;
}

/* Language Button - Match Header Design */
.language-button {
  background: transparent;
  border: none;
  color: #0099E3;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  min-width: auto;
  background-color: #0099E3;
}

.language-button:hover {
  /* background: rgba(255, 255, 255, 0.1); */
  transform: translateY(-1px);
}

.language-button:focus {
  outline: 2px solid rgba(255, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Current Language Display */
.current-language {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.language-icon {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
}

.language-code {
  font-weight: 500;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.9);
}

/* Dropdown Icon */
.dropdown-icon {
  font-size: 0.6rem;
  transition: transform 0.3s ease;
  color: rgba(255, 255, 255, 0.7);
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

/* Language Dropdown */
.language-dropdown {
  position: absolute;
  top: calc(100% + 0.5rem);
  right: 0;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 220px;
  z-index: 1050;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px) scale(0.95);
  transition: all 0.2s ease;
  overflow: hidden;
}

.language-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

/* Dropdown Header */
.dropdown-header {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  padding: 0.75rem 1rem;
  font-weight: 600;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
}

/* Language List */
.language-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 0.5rem 0;
}

/* Language Option */
.language-option {
  width: 100%;
  background: none;
  border: none;
  padding: 0.6rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #495057;
  text-align: left;
  font-size: 0.9rem;
}

.language-option:hover {
  background: #f8f9fa;
  color: #007bff;
}

.language-option.active {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #0056b3;
  font-weight: 600;
}

.language-option .flag {
  font-size: 1.2rem;
  flex-shrink: 0;
}

/* Language Info */
.language-info {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.language-name {
  font-size: 0.95rem;
  font-weight: 500;
  line-height: 1.2;
}

.language-code-small {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 400;
}

.language-option.active .language-code-small {
  color: #0056b3;
}

/* Check Icon */
.check-icon {
  color: #28a745;
  font-size: 0.9rem;
  flex-shrink: 0;
}

/* Dropdown Footer */
.dropdown-footer {
  background: #f8f9fa;
  padding: 0.75rem 1rem;
  border-top: 1px solid #e9ecef;
}

.dropdown-footer small {
  font-size: 0.75rem;
  line-height: 1.3;
}

/* Scrollbar Styling */
.language-list::-webkit-scrollbar {
  width: 4px;
}

.language-list::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.language-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.language-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* RTL Support */
[dir="rtl"] .language-dropdown {
  right: auto;
  left: 0;
}

[dir="rtl"] .dropdown-icon {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .language-option {
  text-align: right;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .language-dropdown {
    min-width: 200px;
    right: -20px;
  }

  .language-button {
    padding: 0.4rem;
  }

  .language-code {
    font-size: 0.75rem;
  }

  .language-icon {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .language-dropdown {
    position: fixed;
    top: auto;
    bottom: 20px;
    left: 20px;
    right: 20px;
    min-width: auto;
    max-height: 60vh;
  }
  
  .language-list {
    max-height: 200px;
  }
}

/* Animation for language change */
.language-selector.changing {
  animation: languageChange 0.5s ease;
}

@keyframes languageChange {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Focus states for accessibility */
.language-option:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
  background: #e3f2fd;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .language-button {
    border: 2px solid white;
  }
  
  .language-dropdown {
    border: 2px solid #000;
  }
  
  .language-option:hover,
  .language-option:focus {
    background: #000;
    color: #fff;
  }
}
