<div class="d-flex justify-content-between align-items-center mb-4">
  <button class="btn btn-outline-secondary" (click)="goBack()">
    ←
  </button>
  <h1 class="mb-0">Admin Profile</h1>
  <button class="btn btn-outline-danger" (click)="logout()">
    Logout
  </button>
</div>

  <div class="profile-wrapper" *ngIf="currentUser">
    <!-- Admin Info Card -->
    <div class="card">
      <div class="card-header text-center">
        <div class="admin-avatar mb-3">
          <i class="fas fa-user-shield"></i>
        </div>
        <h3>{{ userDisplayName }}</h3>
        <p class="text-muted">{{ currentUser.role | titlecase }}</p>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>Email:</strong></label>
            <p class="form-control-plaintext">{{ currentUser.email }}</p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>User ID:</strong></label>
            <p class="form-control-plaintext">#{{ currentUser.id }}</p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>Role:</strong></label>
            <p class="form-control-plaintext">System Administrator</p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>Access Level:</strong></label>
            <p class="form-control-plaintext">Full Access</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0">System Overview</h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-md-4">
            <div class="stat-card">
              <h3 class="text-primary">{{ stats.totalEmployees }}</h3>
              <p class="text-muted">Total Employees</p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="stat-card">
              <h3 class="text-success">{{ stats.totalSurveys }}</h3>
              <p class="text-muted">Surveys Completed</p>
            </div>
          </div>
          <div class="col-md-4">
            <div class="stat-card">
              <h3 class="text-info">{{ stats.completionRate.toFixed(1) }}%</h3>
              <p class="text-muted">Completion Rate</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Admin Actions -->
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0">Admin Actions</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4 mb-3">
            <button class="btn btn-primary w-100" (click)="router.navigate(['/admin/reports'])">
              <i class="fas fa-chart-bar me-2"></i>
              View Reports
            </button>
          </div>
          <div class="col-md-4 mb-3">
            <button class="btn btn-outline-primary w-100" (click)="router.navigate(['/admin/employees'])">
              <i class="fas fa-users me-2"></i>
              Manage Employees
            </button>
          </div>
          <div class="col-md-4 mb-3">
            <button class="btn btn-outline-secondary w-100" (click)="router.navigate(['/admin'])">
              <i class="fas fa-home me-2"></i>
              Dashboard
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- System Info -->
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0">System Information</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>Application:</strong></label>
            <p class="form-control-plaintext">VE Employee Survey System</p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>Version:</strong></label>
            <p class="form-control-plaintext">1.0.0</p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>Last Login:</strong></label>
            <p class="form-control-plaintext">{{ currentDate }}</p>
          </div>
          <div class="col-md-6 mb-3">
            <label class="form-label"><strong>Status:</strong></label>
            <p class="form-control-plaintext text-success">Active</p>
          </div>
        </div>
      </div>
    </div>
  </div>
