import { Routes } from '@angular/router';
import { AdminGuard } from './core/guards/admin.guard';
import { EmployeeGuard } from './core/guards/employee.guard';
import { CompanySuperAdminGuard } from './core/guards/company-super-admin.guard';

export const routes: Routes = [
  // Default route - Login Selector
  {
    path: '',
    loadComponent: () => import('./features/auth/login-selector/login-selector.component').then(m => m.LoginSelectorComponent)
  },

  // Auth routes
  {
    path: 'login',
    loadComponent: () => import('./features/auth/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'employee-login',
    loadComponent: () => import('./features/auth/employee-login/employee-login.component').then(m => m.EmployeeLoginComponent)
  },

  // Employee routes with layout
  {
    path: 'employee',
    // canActivate: [EmployeeGuard],
    loadComponent: () => import('./shared/components/layout/layout.component').then(m => m.LayoutComponent),
    children: [
      {
        path: '',
        loadComponent: () => import('./features/employee/employee-search/employee-search.component').then(m => m.EmployeeSearchComponent)
      },
      {
        path: 'list',
        loadComponent: () => import('./features/employee/employee-list/employee-list.component').then(m => m.EmployeeListComponent)
      },
      {
        path: 'questions',
        loadComponent: () => import('./features/employee/employee-questions/employee-questions.component').then(m => m.EmployeeQuestionsComponent)
      },
      {
        path: 'profile',
        loadComponent: () => import('./features/employee/employee-profile/employee-profile.component').then(m => m.EmployeeProfileComponent)
      }
    ]
  },

  // Admin routes with layout
  {
    path: 'admin',
    canActivate: [AdminGuard],
    loadComponent: () => import('./shared/components/layout/layout.component').then(m => m.LayoutComponent),
    children: [
      {
        path: '',
        loadComponent: () => import('./features/admin/admin-dashboard/admin-dashboard.component').then(m => m.AdminDashboardComponent)
      },
      {
        path: 'reports',
        loadComponent: () => import('./features/admin/admin-reports/admin-reports.component').then(m => m.AdminReportsComponent)
      },
      {
        path: 'profile',
        loadComponent: () => import('./features/admin/admin-profile/admin-profile.component').then(m => m.AdminProfileComponent)
      },
      {
        path: 'departments',
        // canActivate: [CompanySuperAdminGuard],
        loadComponent: () => import('./features/admin/departments/departments.component').then(m => m.DepartmentsComponent)
      },
      {
        path: 'users',
        // canActivate: [CompanySuperAdminGuard],
        loadComponent: () => import('./features/admin/user-management/user-management.component').then(m => m.UserManagementComponent)
      }
    ]
  },

  // Wildcard route
  { path: '**', redirectTo: '/' }
];
