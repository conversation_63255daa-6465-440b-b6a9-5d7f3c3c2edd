import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { BehaviorSubject, Observable, throwError, timer } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';

export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  companyId: number;
}

export interface LoginRequest {
  email: string;
  password: string;
  infiniteToken?: boolean;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  user: User;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private readonly API_URL = 'http://localhost:3000';
  private tokenRefreshTimer: any;

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    private http: HttpClient
  ) {
    // Check if user is already logged in (only in browser)
    if (isPlatformBrowser(this.platformId)) {
      const storedUser = localStorage.getItem('currentUser');
      const storedToken = localStorage.getItem('access_token');

      if (storedUser && storedToken) {
        this.currentUserSubject.next(JSON.parse(storedUser));
        this.scheduleTokenRefresh();
      }
    }
  }

  login(credentials: LoginRequest): Observable<LoginResponse> {
    // Prepare login payload
    const loginPayload = {
      email: credentials.email,
      password: credentials.password,
      ...(credentials.infiniteToken && { infiniteToken: true })
    };

    console.log('Login request payload:', loginPayload);

    return this.http.post<LoginResponse>(`${this.API_URL}/user-auth/login`, loginPayload)
      .pipe(
        tap(response => {
          console.log('Login response received:', response);

          // Store tokens and user in localStorage (only in browser)
          if (isPlatformBrowser(this.platformId)) {
            localStorage.setItem('access_token', response.access_token);
            localStorage.setItem('refresh_token', response.refresh_token);
            localStorage.setItem('currentUser', JSON.stringify(response.user));

            // Store infinite token flag if used
            if (credentials.infiniteToken) {
              localStorage.setItem('infinite_token', 'true');
              console.log('Infinite token flag stored');
            }
          }

          // Update current user
          this.currentUserSubject.next(response.user);

          // Schedule token refresh (skip if infinite token)
          if (!credentials.infiniteToken) {
            this.scheduleTokenRefresh();
          } else {
            console.log('Skipping token refresh due to infinite token');
          }
        }),
        catchError(this.handleError)
      );
  }

  // Employee-specific login method
  employeeLogin(credentials: Omit<LoginRequest, 'infiniteToken'>): Observable<LoginResponse> {
    const employeeCredentials: LoginRequest = {
      ...credentials,
      infiniteToken: true
    };

    console.log('Employee login with infinite token');
    return this.login(employeeCredentials);
  }

  logout(): void {
    // Clear token refresh timer
    if (this.tokenRefreshTimer) {
      clearTimeout(this.tokenRefreshTimer);
      this.tokenRefreshTimer = null;
    }

    if (isPlatformBrowser(this.platformId)) {
      localStorage.removeItem('currentUser');
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('infinite_token');
    }
    this.currentUserSubject.next(null);
    console.log('User logged out, all tokens cleared');
  }

  isAuthenticated(): boolean {
    return this.currentUserSubject.value !== null && this.getAccessToken() !== null;
  }

  private getUserRoleString(user: any): string | null {
    if (!user?.role) return null;
    // Handle both string and object role formats
    return typeof user.role === 'string' ? user.role : user.role.name;
  }

  isAdmin(): boolean {
    const user = this.currentUserSubject.value;
    const roleString = this.getUserRoleString(user);
    if (!roleString) return false;

    // All roles except Employee are considered admin roles
    const adminRoles = ['CompanyAdministrator', 'CompanyManager', 'Supervisor', 'CompanyAdmin', 'companySuperAdmin'];
    return adminRoles.some(role => roleString.toLowerCase() === role.toLowerCase());
  }

  isEmployee(): boolean {
    const user = this.currentUserSubject.value;
    const roleString = this.getUserRoleString(user);
    return roleString?.toLowerCase() === 'employee';
  }

  hasRole(role: string): boolean {
    console.log("role", role)
    console.log("currentUser", this.currentUserSubject.value)
    const user = this.currentUserSubject.value;
    const roleString = this.getUserRoleString(user);
    return roleString?.toLowerCase() === role.toLowerCase();
  }

  hasAnyRole(roles: string[]): boolean {
    const user = this.currentUserSubject.value;
    const roleString = this.getUserRoleString(user);
    if (!roleString) return false;
    return roles.some(role => roleString.toLowerCase() === role.toLowerCase());
  }

  isCompanyAdministrator(): boolean {
    return this.hasRole('CompanyAdministrator');
  }

  isCompanyManager(): boolean {
    return this.hasRole('CompanyManager');
  }

  isSupervisor(): boolean {
    return this.hasRole('Supervisor');
  }

  isCompanyAdmin(): boolean {
    return this.hasRole('CompanyAdmin');
  }

  isCompanySuperAdmin(): boolean {
    return this.hasRole('companySuperAdmin');
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  getAccessToken(): string | null {
    if (isPlatformBrowser(this.platformId)) {
      return localStorage.getItem('access_token');
    }
    return null;
  }

  getRefreshToken(): string | null {
    if (isPlatformBrowser(this.platformId)) {
      return localStorage.getItem('refresh_token');
    }
    return null;
  }

  // For backward compatibility
  getToken(): string | null {
    return this.getAccessToken();
  }

  // Check if infinite token is active
  hasInfiniteToken(): boolean {
    if (isPlatformBrowser(this.platformId)) {
      return localStorage.getItem('infinite_token') === 'true';
    }
    return false;
  }

  refreshToken(): Observable<LoginResponse> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      return throwError(() => new Error('No refresh token available'));
    }

    return this.http.post<LoginResponse>(`${this.API_URL}/user-auth/refresh`, {
      refresh_token: refreshToken
    }).pipe(
      tap(response => {
        // Update tokens in localStorage
        if (isPlatformBrowser(this.platformId)) {
          localStorage.setItem('access_token', response.access_token);
          localStorage.setItem('refresh_token', response.refresh_token);
          localStorage.setItem('currentUser', JSON.stringify(response.user));
        }

        // Update current user
        this.currentUserSubject.next(response.user);

        // Schedule next token refresh
        this.scheduleTokenRefresh();
      }),
      catchError(error => {
        // If refresh fails, logout user
        this.logout();
        return throwError(() => error);
      })
    );
  }

  private scheduleTokenRefresh(): void {
    // Clear existing timer
    if (this.tokenRefreshTimer) {
      clearTimeout(this.tokenRefreshTimer);
    }

    // Schedule refresh for 50 minutes (assuming 1 hour token expiry)
    this.tokenRefreshTimer = setTimeout(() => {
      this.refreshToken().subscribe({
        error: () => {
          // If refresh fails, logout user
          this.logout();
        }
      });
    }, 50 * 60 * 1000); // 50 minutes
  }

  private handleError = (error: HttpErrorResponse) => {
    let errorMessage = 'An error occurred during authentication';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.status === 401) {
      errorMessage = 'Invalid email or password';
    } else if (error.status === 0) {
      errorMessage = 'Unable to connect to server. Please check your connection.';
    }

    return throwError(() => new Error(errorMessage));
  }
}
