import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { UserManagementComponent } from './user-management.component';
import { UserManagementService, User, Role, Department, Supervisor } from '../../../core/services/user-management.service';
import { DepartmentService } from '../../../core/services/department.service';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

describe('UserManagementComponent', () => {
  let component: UserManagementComponent;
  let fixture: ComponentFixture<UserManagementComponent>;
  let mockUserManagementService: jasmine.SpyObj<UserManagementService>;
  let mockDepartmentService: jasmine.SpyObj<DepartmentService>;
  let mockRouter: jasmine.SpyObj<Router>;

  const mockUsers: User[] = [
    {
      id: '1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      role: { id: '1', name: 'Employee' },
      department: { id: '1', name: 'Engineering' },
      supervisor: { id: '2', firstName: 'Jane', lastName: 'Smith', role: 'Supervisor' },
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z'
    }
  ];

  const mockRoles: Role[] = [
    { id: '1', name: 'Employee' },
    { id: '2', name: 'Supervisor' }
  ];

  const mockDepartments: Department[] = [
    { id: '1', name: 'Engineering' },
    { id: '2', name: 'Marketing' }
  ];

  const mockSupervisors: Supervisor[] = [
    { id: '2', firstName: 'Jane', lastName: 'Smith', role: 'Supervisor' }
  ];

  beforeEach(async () => {
    const userManagementServiceSpy = jasmine.createSpyObj('UserManagementService', [
      'getAllUsers',
      'createUser',
      'updateUser',
      'deleteUser',
      'getRoles',
      'getSupervisors',
      'resetPassword',
      'toggleUserStatus'
    ]);
    const departmentServiceSpy = jasmine.createSpyObj('DepartmentService', ['getAllDepartments']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [UserManagementComponent, ReactiveFormsModule, LoadingComponent],
      providers: [
        { provide: UserManagementService, useValue: userManagementServiceSpy },
        { provide: DepartmentService, useValue: departmentServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(UserManagementComponent);
    component = fixture.componentInstance;
    mockUserManagementService = TestBed.inject(UserManagementService) as jasmine.SpyObj<UserManagementService>;
    mockDepartmentService = TestBed.inject(DepartmentService) as jasmine.SpyObj<DepartmentService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should create', () => {
    // Setup mock responses
    mockUserManagementService.getAllUsers.and.returnValue(of(mockUsers));
    mockDepartmentService.getAllDepartments.and.returnValue(of(mockDepartments));
    mockUserManagementService.getRoles.and.returnValue(of(mockRoles));
    mockUserManagementService.getSupervisors.and.returnValue(of(mockSupervisors));

    expect(component).toBeTruthy();
  });

  it('should load initial data on init', () => {
    // Setup mock responses
    mockUserManagementService.getAllUsers.and.returnValue(of(mockUsers));
    mockDepartmentService.getAllDepartments.and.returnValue(of(mockDepartments));
    mockUserManagementService.getRoles.and.returnValue(of(mockRoles));
    mockUserManagementService.getSupervisors.and.returnValue(of(mockSupervisors));
    
    component.ngOnInit();
    
    expect(mockUserManagementService.getAllUsers).toHaveBeenCalled();
    expect(mockDepartmentService.getAllDepartments).toHaveBeenCalled();
    expect(mockUserManagementService.getRoles).toHaveBeenCalled();
    expect(mockUserManagementService.getSupervisors).toHaveBeenCalled();
    expect(component.users).toEqual(mockUsers);
    expect(component.departments).toEqual(mockDepartments);
    expect(component.roles).toEqual(mockRoles);
    expect(component.supervisors).toEqual(mockSupervisors);
  });

  it('should show add form when showAddUserForm is called', () => {
    component.showAddUserForm();
    
    expect(component.showAddForm).toBeTrue();
    expect(component.editingUser).toBeNull();
  });

  it('should show edit form with user data when editUser is called', () => {
    const user = mockUsers[0];
    
    component.editUser(user);
    
    expect(component.showAddForm).toBeTrue();
    expect(component.editingUser).toBe(user);
    expect(component.userForm.get('firstName')?.value).toBe(user.firstName);
    expect(component.userForm.get('lastName')?.value).toBe(user.lastName);
    expect(component.userForm.get('email')?.value).toBe(user.email);
  });

  it('should cancel form and reset state', () => {
    component.showAddForm = true;
    component.editingUser = mockUsers[0];
    component.error = 'Some error';
    
    component.cancelForm();
    
    expect(component.showAddForm).toBeFalse();
    expect(component.editingUser).toBeNull();
    expect(component.error).toBe('');
  });

  it('should navigate back to admin dashboard', () => {
    component.goBack();
    
    expect(mockRouter.navigate).toHaveBeenCalledWith(['/admin']);
  });

  it('should get user role name correctly', () => {
    const user = mockUsers[0];
    const roleName = component.getUserRoleName(user);
    
    expect(roleName).toBe('Employee');
  });

  it('should get user department name correctly', () => {
    const user = mockUsers[0];
    const departmentName = component.getUserDepartmentName(user);
    
    expect(departmentName).toBe('Engineering');
  });

  it('should get user supervisor name correctly', () => {
    const user = mockUsers[0];
    const supervisorName = component.getUserSupervisorName(user);
    
    expect(supervisorName).toBe('Jane Smith');
  });
});
