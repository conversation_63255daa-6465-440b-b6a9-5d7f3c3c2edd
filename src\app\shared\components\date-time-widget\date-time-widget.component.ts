import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { TimeService } from '../../../core/services/time.service';
import { TranslatePipe } from '../../pipes/translate.pipe';

@Component({
  selector: 'app-date-time-widget',
  standalone: true,
  imports: [CommonModule, TranslatePipe],
  templateUrl: './date-time-widget.component.html',
  styleUrls: ['./date-time-widget.component.css']
})
export class DateTimeWidgetComponent implements OnInit, OnDestroy {
  currentTime: string = '';
  currentDate: string = '';
  private subscriptions: Subscription[] = [];

  constructor(private timeService: TimeService) {}

  ngOnInit(): void {
    // Subscribe to current time
    this.subscriptions.push(
      this.timeService.getCurrentTime().subscribe(time => {
        this.currentTime = time;
      })
    );

    // Get current date
    this.currentDate = this.timeService.getCurrentDate();

    // Update date every hour (in case the date changes)
    this.subscriptions.push(
      this.timeService.getCurrentTime().subscribe(() => {
        this.currentDate = this.timeService.getCurrentDate();
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }
}
