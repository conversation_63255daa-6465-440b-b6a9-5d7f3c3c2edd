import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { delay, catchError, map } from 'rxjs/operators';
import { AuthService } from './auth.service';

export interface Employee {
  id: number;
  name: string;
  email: string;
  department: {
    id: string;
    name: string;
  };
  position: string;
  hasSubmittedSurveyToday: boolean;
  dateOfBirth?: string;
  avatar?: string;
  firstName?: string;
  lastName?: string;
  isActive?: boolean;
  companyId?: string;

}

export interface EmployeeSearchFilters {
  nameStartsWith?: string;
  isActive?: boolean;
  department?: string;
  companyId?: string;
}

export interface EmployeeSearchResponse {
  employees: Employee[];
  totalCount: number;
  page?: number;
  limit?: number;
}

export interface SurveyQuestion {
  id: number;
  question: string;
  options: string[];
  type: 'single' | 'multiple';
}

export interface SurveyResponse {
  employeeId: number;
  questionId: number | string;
  answer: string | string[];
  timestamp: Date;
}

@Injectable({
  providedIn: 'root'
})
export class EmployeeService {
  private readonly API_URL = 'http://localhost:3000/api';

  // Mock employee data
  private mockEmployees: Employee[] = [
    { id: 1, name: 'David Smith', firstName: 'David', lastName: 'Smith', email: '<EMAIL>', department: {name: 'Engineering', id: "1"}, position: 'Senior Developer', avatar: 'assets/images/avtar-1.png', hasSubmittedSurveyToday: false },
    { id: 2, name: 'Daniel Wilson', firstName: 'Daniel', lastName: 'Wilson', email: '<EMAIL>', department:{name: 'Marketing', id: "2"}, position: 'Marketing Manager', avatar: 'assets/images/avtar-2.png', hasSubmittedSurveyToday: true },
    { id: 3, name: 'Daisy Brown', firstName: 'Daisy', lastName: 'Brown', email: '<EMAIL>', department: {name: 'HR', id: "3"}, position: 'HR Specialist', avatar: 'assets/images/avtar-3.png', hasSubmittedSurveyToday: false },
    { id: 4, name: 'Dominic Thomas', firstName: 'Dominic', lastName: 'Thomas', email: '<EMAIL>', department: {name: "Sales", id: "4"}, position: 'Sales Representative', hasSubmittedSurveyToday: true },
    { id: 5, name: 'Dawson Green', firstName: 'Dawson', lastName: 'Green', email: '<EMAIL>', department: {name: 'Engineering', id: "1"}, position: 'Junior Developer', hasSubmittedSurveyToday: false },
    { id: 6, name: 'Diana Robinson', firstName: 'Diana', lastName: 'Robinson', email: '<EMAIL>', department: {name: 'Finance', id: "5"}, position: 'Accountant', hasSubmittedSurveyToday: true },
    { id: 7, name: 'Derek Johnson', firstName: 'Derek', lastName: 'Johnson', email: '<EMAIL>', department: {name: 'Operations', id: "6"}, position: 'Operations Manager', hasSubmittedSurveyToday: false },
    { id: 8, name: 'Donna Davis', firstName: 'Donna', lastName: 'Davis', email: '<EMAIL>', department: {name: 'Design', id: "7"}, position: 'UI/UX Designer', hasSubmittedSurveyToday: false },
    { id: 9, name: 'Dylan Miller', firstName: 'Dylan', lastName: 'Miller', email: '<EMAIL>', department: {name: 'Engineering', id: "1"}, position: 'DevOps Engineer', hasSubmittedSurveyToday: true },
    { id: 10, name: 'Delilah Garcia', firstName: 'Delilah', lastName: 'Garcia', email: '<EMAIL>', department: {name: 'Marketing', id: "2"}, position: 'Content Creator', hasSubmittedSurveyToday: false }
  ];

  // Mock survey questions
  private mockQuestions: SurveyQuestion[] = [
    {
      id: 1,
      question: 'How are you doing today?',
      options: ['Pretty Good', 'Not Well', 'Awesome!'],
      type: 'single'
    },
    {
      id: 2,
      question: 'Today I am going to do',
      options: ['Finish Task', 'Play Cricket', 'Watch Movie', 'Learn Something New'],
      type: 'single'
    },
    {
      id: 3,
      question: 'What motivates you at work?',
      options: ['Team Collaboration', 'Learning Opportunities', 'Recognition', 'Work-Life Balance'],
      type: 'multiple'
    }
  ];

  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) { }

  private getAuthHeaders(): HttpHeaders {
    const token = this.authService.getToken();
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    });
  }

  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred!';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      if (error.error && error.error.message) {
        errorMessage = error.error.message;
      }
    }

    console.error('Employee Service Error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  private getCurrentCompanyId(): string | null {
    const user = this.authService.getCurrentUser();
    console.log("user", user)
    return user?.companyId?.toString() || null;
  }

  // Get all employees
  getAllEmployees(): Observable<Employee[]> {
    return of(this.mockEmployees).pipe(delay(500));
  }

  // Get employees by first letter (API Integration)
  getEmployeesByLetter(letter: string): Observable<Employee[]> {
    const companyId = this.getCurrentCompanyId();

    if (!companyId) {
      console.warn('No company ID found, falling back to mock data');
      const filtered = this.mockEmployees.filter(emp =>
        emp.name.toUpperCase().startsWith(letter.toUpperCase())
      );
      return of(filtered).pipe(delay(300));
    }

    const headers = this.getAuthHeaders();
    const url = `${this.API_URL}/users/company/${companyId}/employees`;

    // Build query parameters
    const params = new URLSearchParams();
    params.append('nameStartsWith', letter);
    params.append('isActive', 'true'); // Only get active employees

    const fullUrl = `${url}?${params.toString()}`;

    console.log('Fetching employees by letter from API:', fullUrl);

    return this.http.get<any>(fullUrl, { headers }).pipe(
      // Transform API response to Employee array
      map((response: any) => {
        console.log('API Response:', response);
        // Handle different response formats
        if (Array.isArray(response)) {
          return response;
        } else if (response && Array.isArray(response.users)) {
          return response.users;
        } else if (response && Array.isArray(response.data)) {
          return response.data;
        } else {
          console.warn('Unexpected API response format:', response);
          return [];
        }
      }),
      catchError((error) => {
        console.error('API Error, falling back to mock data:', error);
        // Fallback to mock data on API error
        const filtered = this.mockEmployees.filter(emp =>
          emp.name.toUpperCase().startsWith(letter.toUpperCase())
        );
        return of(filtered);
      })
    );
  }

  // Advanced employee search with multiple filters (Full API Integration)
  searchEmployeesByFilters(filters: EmployeeSearchFilters): Observable<EmployeeSearchResponse> {
    const companyId = this.getCurrentCompanyId();

    if (!companyId) {
      console.warn('No company ID found, using mock data');
      const mockResponse: EmployeeSearchResponse = {
        employees: this.mockEmployees,
        totalCount: this.mockEmployees.length
      };
      return of(mockResponse).pipe(delay(300));
    }

    const headers = this.getAuthHeaders();
    const url = `${this.API_URL}/users/company/${companyId}/employees`;

    // Build query parameters
    const params = new URLSearchParams();

    if (filters.nameStartsWith) {
      params.append('nameStartsWith', filters.nameStartsWith);
    }

    if (filters.isActive !== undefined) {
      params.append('isActive', filters.isActive.toString());
    }

    if (filters.department) {
      params.append('department', filters.department);
    }

    const fullUrl = params.toString() ? `${url}?${params.toString()}` : url;

    console.log('Searching employees with filters:', fullUrl);

    return this.http.get<any>(fullUrl, { headers }).pipe(
      // Transform response to match our interface
      map((response: any) => {
        console.log('Filter Search API Response:', response);
        let employees: Employee[] = [];

        if (Array.isArray(response)) {
          employees = response;
        } else if (response && Array.isArray(response.employees)) {
          employees = response.employees;
        } else if (response && Array.isArray(response.data)) {
          employees = response.data;
        } else if (response && response.users && Array.isArray(response.users)) {
          employees = response.users;
        }

        return {
          employees: employees,
          totalCount: employees.length
        } as EmployeeSearchResponse;
      }),
      catchError((error) => {
        console.error('Employee search API error:', error);
        // Fallback to mock data
        const mockResponse: EmployeeSearchResponse = {
          employees: this.mockEmployees,
          totalCount: this.mockEmployees.length
        };
        return of(mockResponse);
      })
    );
  }

  // Comprehensive employee search method matching your curl example exactly
  getCompanyEmployees(
    companyId?: string,
    nameStartsWith?: string,
    isActive?: boolean,
    department?: string
  ): Observable<Employee[]> {
    const currentCompanyId = companyId || this.getCurrentCompanyId();

    if (!currentCompanyId) {
      console.warn('No company ID provided, using mock data');
      let filtered = this.mockEmployees;

      if (nameStartsWith) {
        filtered = filtered.filter(emp =>
          emp.name.toUpperCase().startsWith(nameStartsWith.toUpperCase())
        );
      }

      if (department) {
        filtered = filtered.filter(emp =>
          emp.department.name.toLowerCase() === department.toLowerCase()
        );
      }

      return of(filtered).pipe(delay(300));
    }

    const headers = this.getAuthHeaders();
    const url = `${this.API_URL}/users/company/${currentCompanyId}/employees`;

    // Build query parameters exactly as in your curl example
    const params = new URLSearchParams();

    if (nameStartsWith) {
      params.append('nameStartsWith', nameStartsWith);
    }

    if (isActive !== undefined) {
      params.append('isActive', isActive.toString());
    }

    if (department) {
      params.append('department', department);
    }

    const fullUrl = params.toString() ? `${url}?${params.toString()}` : url;

    console.log('API Request URL:', fullUrl);
    console.log('Request Headers:', {
      'Authorization': `Bearer ${this.authService.getToken()}`,
      'Content-Type': 'application/json'
    });

    return this.http.get<any>(fullUrl, { headers }).pipe(
      // Transform API response to Employee array
      map((response: any) => {
        console.log('Company Employees API Response:', response);
        if (Array.isArray(response)) {
          return response;
        } else if (response && Array.isArray(response.employees)) {
          return response.employees;
        } else if (response && Array.isArray(response.data)) {
          return response.data;
        } else if (response && response.users && Array.isArray(response.users)) {
          return response.users;
        } else {
          console.warn('Unexpected response format:', response);
          return [];
        }
      }),
      catchError((error) => {
        console.error('Company employees API error:', error);
        console.error('Error details:', {
          status: error.status,
          message: error.message,
          url: fullUrl
        });

        // Fallback to mock data with applied filters
        let filtered = this.mockEmployees;

        if (nameStartsWith) {
          filtered = filtered.filter(emp =>
            emp.name.toUpperCase().startsWith(nameStartsWith.toUpperCase())
          );
        }

        if (department) {
          filtered = filtered.filter(emp =>
            emp.department.name.toLowerCase() === department.toLowerCase()
          );
        }

        return of(filtered);
      })
    );
  }

  // Get employee by ID (API Integration)
  getEmployeeById(id: string): Observable<Employee | undefined> {
    if (!id) {
      console.warn('No employee ID provided');
      return of(undefined);
    }

    const headers = this.getAuthHeaders();
    const url = `${this.API_URL}/users/${id}`;

    console.log('Fetching employee by ID from API:', url);

    return this.http.get<any>(url, { headers }).pipe(
      // Transform API response to Employee
      map((response: any) => {
        console.log('Employee by ID API Response:', response);

        if (response) {
          // Transform API user object to Employee interface
          const employee: Employee = {
            id: response.id || response.userId,
            name: response.firstName && response.lastName
              ? `${response.firstName} ${response.lastName}`
              : response.name || response.fullName || 'Unknown',
            firstName: response.firstName,
            lastName: response.lastName,
            email: response.email,
            position: response.position || response.jobTitle || response.role?.name || 'Unknown',
            isActive: response.isActive !== undefined ? response.isActive : true,
            dateOfBirth: response.dateOfBirth,
            avatar: response.avatar || response.profilePicture,
            companyId: response.companyId,
            department: response.department,
            hasSubmittedSurveyToday: response.hasSubmittedSurveyToday
          };

          return employee;
        }

        return undefined;
      }),
      catchError((error) => {
        console.error('Employee by ID API error:', error);
        console.error('Error details:', {
          status: error.status,
          message: error.message,
          url: url
        });

        // Fallback to mock data
        const employee = this.mockEmployees.find(emp => emp.id.toString() === id.toString());
        return of(employee);
      })
    );
  }

  // Search employees by name (Enhanced with API)
  searchEmployees(searchTerm: string): Observable<Employee[]> {
    const companyId = this.getCurrentCompanyId();

    if (!companyId) {
      console.warn('No company ID found, using mock data for search');
      const filtered = this.mockEmployees.filter(emp =>
        emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        emp.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        emp.department.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      return of(filtered).pipe(delay(300));
    }

    const headers = this.getAuthHeaders();
    const url = `${this.API_URL}/users/company/${companyId}/employees`;

    // Build query parameters for general search
    const params = new URLSearchParams();
    params.append('nameStartsWith', searchTerm);
    params.append('isActive', 'true');

    const fullUrl = `${url}?${params.toString()}`;

    console.log('Searching employees via API:', fullUrl);

    return this.http.get<any>(fullUrl, { headers }).pipe(
      // Transform API response to Employee array
      map((response: any) => {
        console.log('Search API Response:', response);
        if (Array.isArray(response)) {
          return response;
        } else if (response && Array.isArray(response.employees)) {
          return response.employees;
        } else if (response && Array.isArray(response.data)) {
          return response.data;
        } else {
          return [];
        }
      }),
      catchError((error) => {
        console.error('Employee search API error, falling back to mock data:', error);
        // Fallback to mock data on API error
        const filtered = this.mockEmployees.filter(emp =>
          emp.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          emp.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
          emp.department.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        return of(filtered);
      })
    );
  }

  // Get employees by department (New API method)
  getEmployeesByDepartment(department: string, isActive: boolean = true): Observable<Employee[]> {
    const companyId = this.getCurrentCompanyId();

    if (!companyId) {
      console.warn('No company ID found, using mock data for department filter');
      const filtered = this.mockEmployees.filter(emp =>
        emp.department.name.toLowerCase() === department.toLowerCase()
      );
      return of(filtered).pipe(delay(300));
    }

    const headers = this.getAuthHeaders();
    const url = `${this.API_URL}/users/company/${companyId}/employees`;

    // Build query parameters
    const params = new URLSearchParams();
    params.append('department', department);
    params.append('isActive', isActive.toString());

    const fullUrl = `${url}?${params.toString()}`;

    console.log('Fetching employees by department from API:', fullUrl);

    return this.http.get<any>(fullUrl, { headers }).pipe(
      // Transform API response to Employee array
      map((response: any) => {
        console.log('Department API Response:', response);
        if (Array.isArray(response)) {
          return response;
        } else if (response && Array.isArray(response.employees)) {
          return response.employees;
        } else if (response && Array.isArray(response.data)) {
          return response.data;
        } else {
          return [];
        }
      }),
      catchError((error) => {
        console.error('Department filter API error, falling back to mock data:', error);
        // Fallback to mock data on API error
        const filtered = this.mockEmployees.filter(emp =>
          emp.department.name.toLowerCase() === department.toLowerCase()
        );
        return of(filtered);
      })
    );
  }

  // Get survey questions
  getSurveyQuestions(): Observable<SurveyQuestion[]> {
    return of(this.mockQuestions).pipe(delay(300));
  }

  // Submit survey response
  submitSurveyResponse(response: SurveyResponse): Observable<boolean> {
    // In a real app, this would save to a database
    console.log('Survey response submitted:', response);
    return of(true).pipe(delay(500));
  }

  // Add new employee (admin only)
  addEmployee(employee: Omit<Employee, 'id'>): Observable<Employee> {
    const newEmployee: Employee = {
      ...employee,
      id: Math.max(...this.mockEmployees.map(e => e.id)) + 1
    };
    this.mockEmployees.push(newEmployee);
    return of(newEmployee).pipe(delay(500));
  }

  // Update employee (admin only)
  updateEmployee(id: number, employee: Partial<Employee>): Observable<Employee | null> {
    const index = this.mockEmployees.findIndex(e => e.id === id);
    if (index !== -1) {
      this.mockEmployees[index] = { ...this.mockEmployees[index], ...employee };
      return of(this.mockEmployees[index]).pipe(delay(500));
    }
    return of(null).pipe(delay(500));
  }

  // Delete employee (admin only)
  deleteEmployee(id: number): Observable<boolean> {
    const index = this.mockEmployees.findIndex(e => e.id === id);
    if (index !== -1) {
      this.mockEmployees.splice(index, 1);
      return of(true).pipe(delay(500));
    }
    return of(false).pipe(delay(500));
  }

  // Test API connection method
  testEmployeeAPI(): Observable<any> {
    const companyId = this.getCurrentCompanyId();
    const user = this.authService.getCurrentUser();

    console.log('Testing Employee API Connection:');
    console.log('Current User:', user);
    console.log('Company ID:', companyId);
    console.log('Token:', this.authService.getToken() ? 'Present' : 'Missing');

    if (!companyId) {
      return throwError(() => new Error('No company ID found'));
    }

    // Test with a simple request for employees starting with 'J'
    return this.getCompanyEmployees(companyId, 'J', true, 'Sales');
  }

  // Test getting employee by ID
  testGetEmployeeById(employeeId: string): Observable<Employee | undefined> {
    console.log('Testing Get Employee By ID API:');
    console.log('Employee ID:', employeeId);
    console.log('API URL:', `${this.API_URL}/users/${employeeId}`);
    console.log('Token:', this.authService.getToken() ? 'Present' : 'Missing');

    return this.getEmployeeById(employeeId);
  }

  // Get current user as employee
  getCurrentUserAsEmployee(): Observable<Employee | undefined> {
    const currentUser = this.authService.getCurrentUser();

    if (!currentUser || !currentUser.id) {
      console.warn('No current user found');
      return of(undefined);
    }

    console.log('Getting current user as employee:', currentUser.id);
    return this.getEmployeeById(currentUser.id.toString());
  }
}
