import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';

export interface Language {
  code: string;
  name: string;
  flag: string;
  rtl?: boolean;
}

export interface TranslationData {
  [key: string]: string | TranslationData;
}

@Injectable({
  providedIn: 'root'
})
export class TranslationService {
  private currentLanguageSubject = new BehaviorSubject<string>('en');
  public currentLanguage$ = this.currentLanguageSubject.asObservable();

  private translationsSubject = new BehaviorSubject<TranslationData>({});
  public translations$ = this.translationsSubject.asObservable();

  // Language change event emitter
  private languageChangeSubject = new Subject<string>();
  public languageChange$ = this.languageChangeSubject.asObservable();

  // Supported languages
  public readonly supportedLanguages: Language[] = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
    // { code: 'fr', name: 'Fran<PERSON>', flag: '🇫🇷' },
    // { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '🇩🇪' },
    // { code: 'ar', name: 'العربية', flag: '🇸🇦', rtl: true },
    // { code: 'zh', name: '中文', flag: '🇨🇳' },
    // { code: 'hi', name: 'हिंदी', flag: '🇮🇳' },
    // { code: 'pt', name: 'Português', flag: '🇵🇹' }
  ];

  private translations: { [lang: string]: TranslationData } = {};

  constructor() {
    this.loadStoredLanguage();
    this.loadTranslations();
  }

  private loadStoredLanguage(): void {
    const storedLang = localStorage.getItem('selectedLanguage');
    if (storedLang && this.isLanguageSupported(storedLang)) {
      this.currentLanguageSubject.next(storedLang);
    } else {
      // Detect browser language
      const browserLang = navigator.language.split('-')[0];
      const defaultLang = this.isLanguageSupported(browserLang) ? browserLang : 'en';
      this.currentLanguageSubject.next(defaultLang);
    }
  }

  private isLanguageSupported(langCode: string): boolean {
    return this.supportedLanguages.some(lang => lang.code === langCode);
  }

  private async loadTranslations(): Promise<void> {
    const currentLang = this.currentLanguageSubject.value;
    
    try {
      // Dynamic import of translation files
      const translationModule = await import(`../../../assets/i18n/${currentLang}.json`);
      this.translations[currentLang] = translationModule.default || translationModule;
      this.translationsSubject.next(this.translations[currentLang]);
    } catch (error) {
      console.warn(`Translation file for ${currentLang} not found, falling back to English`);
      
      // Fallback to English
      try {
        const fallbackModule = await import(`../../../assets/i18n/en.json`);
        this.translations['en'] = fallbackModule.default || fallbackModule;
        this.translationsSubject.next(this.translations['en']);
      } catch (fallbackError) {
        console.error('Failed to load fallback translations:', fallbackError);
        this.translationsSubject.next({});
      }
    }
  }

  public async setLanguage(langCode: string): Promise<void> {
    if (!this.isLanguageSupported(langCode)) {
      console.warn(`Language ${langCode} is not supported`);
      return;
    }

    console.log(`🌐 TranslationService: Changing language to ${langCode}`);

    this.currentLanguageSubject.next(langCode);
    localStorage.setItem('selectedLanguage', langCode);

    // Load translations for the new language
    await this.loadTranslations();

    // Update document direction for RTL languages
    this.updateDocumentDirection(langCode);

    // Emit language change event
    this.languageChangeSubject.next(langCode);

    console.log(`✅ TranslationService: Language changed to ${langCode} successfully`);
  }

  private updateDocumentDirection(langCode: string): void {
    const language = this.supportedLanguages.find(lang => lang.code === langCode);
    const direction = language?.rtl ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('dir', direction);
    document.documentElement.setAttribute('lang', langCode);
  }

  public getCurrentLanguage(): string {
    return this.currentLanguageSubject.value;
  }

  public getCurrentLanguageInfo(): Language | undefined {
    const currentLang = this.getCurrentLanguage();
    return this.supportedLanguages.find(lang => lang.code === currentLang);
  }

  public translate(key: string, params?: { [key: string]: string | number }): string {
    const translations = this.translationsSubject.value;
    const translation = this.getNestedTranslation(translations, key);
    
    if (!translation) {
      console.warn(`Translation not found for key: ${key}`);
      return key; // Return the key itself if translation not found
    }

    // Replace parameters in translation
    if (params) {
      return this.replaceParams(translation, params);
    }

    return translation;
  }

  private getNestedTranslation(obj: TranslationData, key: string): string | null {
    const keys = key.split('.');
    let current: any = obj;

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return null;
      }
    }

    return typeof current === 'string' ? current : null;
  }

  private replaceParams(translation: string, params: { [key: string]: string | number }): string {
    let result = translation;
    
    Object.keys(params).forEach(key => {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), String(params[key]));
    });

    return result;
  }

  public getTranslation(key: string): Observable<string> {
    return new Observable(observer => {
      const subscription = this.translations$.subscribe(() => {
        observer.next(this.translate(key));
      });

      // Initial value
      observer.next(this.translate(key));

      return () => subscription.unsubscribe();
    });
  }
}
