import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class EmployeeGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(
    _route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    const isAuthenticated = this.authService.isAuthenticated();
    const isEmployee = this.authService.isEmployee();
    const currentUser = this.authService.getCurrentUser();

    console.log('EmployeeGuard - Access attempt to:', state.url);
    console.log('EmployeeGuard - Is authenticated:', isAuthenticated);
    console.log('EmployeeGuard - Is employee:', isEmployee);
    console.log('EmployeeGuard - Current user role:', currentUser?.role);

    if (isAuthenticated && isEmployee) {
      console.log('EmployeeGuard - Access granted to employee');
      return true;
    } else if (isAuthenticated) {
      // User is authenticated but not an employee - redirect to admin dashboard
      console.log('EmployeeGuard - User is not employee, redirecting to admin dashboard');
      this.router.navigate(['/admin']);
      return false;
    } else {
      // User is not authenticated
      console.log('EmployeeGuard - User not authenticated, redirecting to employee login');
      this.router.navigate(['/employee-login'], { queryParams: { returnUrl: state.url } });
      return false;
    }
  }
}
