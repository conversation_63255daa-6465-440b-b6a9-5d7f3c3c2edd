/* ===== USER MANAGEMENT STYLES ===== */

/* Header Section */
.header-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-title h1 {
  color: #2c3e50;
  font-weight: 600;
  font-size: 1.75rem;
}

.back-btn, .add-btn {
  border-radius: 0.5rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.add-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

/* Alerts Container */
.alerts-container {
  margin-bottom: 1.5rem;
}

.alert {
  border: none;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-weight: 500;
}

.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border-left: 4px solid #28a745;
}

.alert-danger {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left: 4px solid #dc3545;
}

/* Form Card */
.form-card .card {
  border-radius: 0.75rem;
  overflow: hidden;
}

.form-card .card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
  padding: 1.25rem 1.5rem;
}

.form-card .card-body {
  background-color: #fff;
}

/* Form Styles */
.form-label {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-control, .form-select {
  border: 2px solid #e9ecef;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  background-color: #fff;
}

.form-control-lg {
  padding: 1rem 1.25rem;
  font-size: 1rem;
}

.form-control:focus, .form-select:focus {
  border-color: #007bff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
  background-color: #fff;
}

.form-control.is-invalid, .form-select.is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #dc3545;
  font-weight: 500;
}

.form-text {
  margin-top: 0.5rem;
}

.form-text i {
  color: #6c757d;
}

.form-actions {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e9ecef;
}

/* Form Switch */
.form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.form-check-input:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Stats Cards */
.stats-card {
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1) !important;
}

.stats-icon {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* Users Table Card */
.users-table-card {
  border-radius: 0.75rem;
  overflow: hidden;
}

.users-table-card .card-header {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  padding: 1.25rem 1.5rem;
}

/* Table Styles */
.table {
  margin-bottom: 0;
  font-size: 0.95rem;
}

.table thead th {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  color: #495057;
  font-weight: 600;
  padding: 1rem 1.25rem;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tbody td {
  padding: 1.25rem;
  vertical-align: middle;
  border-bottom: 1px solid #f1f3f4;
}

.user-row {
  transition: all 0.2s ease;
}

.user-row:hover {
  background-color: #f8f9fa;
  transform: scale(1.001);
}

.user-info strong {
  font-size: 1rem;
  color: #2c3e50;
}

.user-email {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Badge Styles */
.badge {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
}

/* Action Menu Button */
.action-menu-btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border-width: 1.5px;
  padding: 0.375rem 0.75rem;
  position: relative;
  overflow: hidden;
}

.action-menu-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-color: #007bff;
  color: white;
}

.action-menu-btn:active {
  transform: translateY(0);
}

.action-menu-btn i {
  transition: all 0.2s ease;
}

.action-menu-btn:hover i {
  transform: rotate(90deg);
}

/* ===== USER ACTIONS MODAL ===== */

/* Modal Enhancements */
.modal-content {
  border-radius: 0.75rem;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}

.modal-header {
  border-bottom: 2px solid #e9ecef;
  border-radius: 0.75rem 0.75rem 0 0;
  padding: 1.25rem 1.5rem;
}

.modal-title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  border-top: 1px solid #e9ecef;
  border-radius: 0 0 0.75rem 0.75rem;
  padding: 1rem 1.5rem;
}

/* User Info Header */
.user-info-header {
  border: 1px solid #e9ecef;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.user-avatar {
  flex-shrink: 0;
}

.avatar-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

/* Action Grid */
.action-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
}

.action-card {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 0.5rem;
  border-width: 1.5px;
  text-align: left;
  transition: all 0.3s ease;
  background: white;
  position: relative;
  overflow: hidden;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-card:hover::before {
  left: 100%;
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  flex-shrink: 0;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.action-content {
  flex: 1;
}

.action-content h6 {
  margin-bottom: 0.25rem;
  font-weight: 600;
  color: #2c3e50;
}

.action-content small {
  color: #6c757d;
  line-height: 1.3;
}

/* Specific Action Card Styles */
.btn-outline-primary.action-card .action-icon {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

.btn-outline-primary.action-card:hover {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-color: #007bff;
  color: white;
}

.btn-outline-primary.action-card:hover .action-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.btn-outline-info.action-card .action-icon {
  background: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
}

.btn-outline-info.action-card:hover {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  border-color: #17a2b8;
  color: white;
}

.btn-outline-info.action-card:hover .action-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.btn-outline-success.action-card .action-icon {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.btn-outline-success.action-card:hover {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  border-color: #28a745;
  color: white;
}

.btn-outline-success.action-card:hover .action-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.btn-outline-warning.action-card .action-icon {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.btn-outline-warning.action-card:hover {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  border-color: #ffc107;
  color: #212529;
}

.btn-outline-warning.action-card:hover .action-icon {
  background: rgba(33, 37, 41, 0.1);
  color: #212529;
}

.btn-outline-danger.action-card .action-icon {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.btn-outline-danger.action-card:hover {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  border-color: #dc3545;
  color: white;
}

.btn-outline-danger.action-card:hover .action-icon {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Empty State */
.empty-state {
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0.75rem;
  margin: 2rem 0;
}

.empty-icon {
  margin-bottom: 1.5rem;
}

.empty-state h4 {
  color: #6c757d;
  font-weight: 600;
}

.empty-state p {
  color: #6c757d;
  font-size: 1.1rem;
  max-width: 400px;
  margin: 0 auto;
}

/* Button Enhancements */
.btn {
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 123, 255, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  border: none;
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
  background: linear-gradient(135deg, #1e7e34 0%, #155724 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(40, 167, 69, 0.4);
}

.btn-outline-primary:hover {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  transform: translateY(-1px);
}

.btn-outline-danger:hover {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  transform: translateY(-1px);
}

.btn-outline-secondary:hover {
  background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
  transform: translateY(-1px);
}

.btn-outline-info:hover {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
  transform: translateY(-1px);
}

.btn-outline-warning:hover {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  transform: translateY(-1px);
}

.btn-outline-success:hover {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  transform: translateY(-1px);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop Styles */
@media (min-width: 1400px) {
  .header-section {
    padding: 2rem;
  }

  .page-title h1 {
    font-size: 2rem;
  }

  .table thead th {
    padding: 1.5rem 1.25rem;
    font-size: 1rem;
  }

  .table tbody td {
    padding: 1.5rem 1.25rem;
  }
}

/* Desktop Styles */
@media (min-width: 1200px) and (max-width: 1399px) {
  .header-section {
    padding: 1.75rem;
  }

  .page-title h1 {
    font-size: 1.85rem;
  }
}

/* Tablet Landscape Styles */
@media (min-width: 992px) and (max-width: 1199px) {
  .header-section {
    padding: 1.5rem;
  }

  .page-title h1 {
    font-size: 1.6rem;
  }

  .table thead th {
    padding: 1rem 0.75rem;
    font-size: 0.9rem;
  }

  .table tbody td {
    padding: 1rem 0.75rem;
  }

  .action-btn {
    width: 30px;
    height: 30px;
  }

  .action-btn i {
    font-size: 0.8rem;
  }
}

/* Tablet Portrait Styles */
@media (min-width: 768px) and (max-width: 991px) {
  .header-section {
    padding: 1.25rem;
  }

  .header-section .container-fluid {
    padding: 0;
  }

  .page-title h1 {
    font-size: 1.4rem;
  }

  .back-btn, .add-btn {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
  }

  .stats-card {
    margin-bottom: 1rem;
  }

  .table thead th {
    padding: 0.875rem 0.5rem;
    font-size: 0.85rem;
  }

  .table tbody td {
    padding: 0.875rem 0.5rem;
  }

  .user-info strong {
    font-size: 0.95rem;
  }

  .badge {
    font-size: 0.7rem;
    padding: 0.375rem 0.625rem;
  }

  .action-menu-btn {
    font-size: 0.875rem;
    padding: 0.375rem 0.625rem;
  }

  /* Modal adjustments for tablet */
  .modal-dialog {
    max-width: 500px;
  }

  .action-card {
    padding: 0.875rem;
  }

  .action-icon {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
}

/* Mobile Styles */
@media (max-width: 767px) {
  .header-section {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .header-section .container-fluid {
    padding: 0;
  }

  .page-title h1 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .back-btn, .add-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .form-card .card-body {
    padding: 1.5rem;
  }

  .stats-card .card-body {
    padding: 1.5rem;
  }

  .stats-icon {
    width: 50px;
    height: 50px;
  }

  .stats-icon i {
    font-size: 1.5rem;
  }

  .users-table-card .card-header {
    padding: 1rem;
  }

  .table thead th {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }

  .table tbody td {
    padding: 0.75rem 0.5rem;
  }

  .user-info strong {
    font-size: 0.9rem;
  }

  .user-info small {
    font-size: 0.75rem;
  }

  .user-email {
    max-width: 150px;
  }

  .badge {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
  }

  .action-menu-btn {
    font-size: 0.8rem;
    padding: 0.375rem 0.5rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }

  .empty-state h4 {
    font-size: 1.1rem;
  }

  .empty-state p {
    font-size: 0.95rem;
  }

  /* Mobile modal styles */
  .modal-dialog {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }

  .modal-header {
    padding: 1rem;
  }

  .modal-body {
    padding: 1rem;
  }

  .modal-footer {
    padding: 0.75rem 1rem;
  }

  .modal-title {
    font-size: 1rem;
  }

  .user-info-header {
    padding: 0.75rem;
  }

  .avatar-circle {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .action-card {
    padding: 0.75rem;
  }

  .action-icon {
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
    margin-right: 0.75rem;
  }

  .action-content h6 {
    font-size: 0.9rem;
  }

  .action-content small {
    font-size: 0.75rem;
  }
}

/* Small Mobile Styles */
@media (max-width: 576px) {
  .header-section {
    padding: 0.75rem;
  }

  .header-section .row {
    margin: 0;
  }

  .header-section .col-6,
  .header-section .col-12 {
    padding: 0.25rem;
  }

  .page-title h1 {
    font-size: 1.1rem;
  }

  .back-btn, .add-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
  }

  .form-card .card-body {
    padding: 1rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }

  .stats-card .card-body {
    padding: 1rem;
  }

  .table-responsive {
    font-size: 0.75rem;
  }

  .table thead th {
    padding: 0.5rem 0.25rem;
    font-size: 0.7rem;
  }

  .table tbody td {
    padding: 0.5rem 0.25rem;
  }

  .user-info strong {
    font-size: 0.85rem;
  }

  .user-info small {
    font-size: 0.7rem;
  }

  .user-email {
    max-width: 120px;
    font-size: 0.75rem;
  }

  .badge {
    font-size: 0.6rem;
    padding: 0.2rem 0.4rem;
  }

  .dropdown-toggle {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* Tablet Landscape Orientation */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
  .header-section {
    padding: 1.25rem 2rem;
  }

  .table thead th {
    padding: 1rem 0.75rem;
  }

  .table tbody td {
    padding: 1rem 0.75rem;
  }

  .action-buttons .btn {
    padding: 0.375rem 0.625rem;
  }
}

/* Tablet Portrait Orientation */
@media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
  .header-section {
    padding: 1.5rem;
  }

  .page-title h1 {
    font-size: 1.5rem;
  }

  .table thead th {
    padding: 0.875rem 0.625rem;
    font-size: 0.875rem;
  }

  .table tbody td {
    padding: 0.875rem 0.625rem;
  }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
  .btn {
    min-height: 44px; /* Minimum touch target size */
    min-width: 44px;
  }

  .action-btn {
    min-height: 40px !important;
    min-width: 40px !important;
    width: 40px !important;
    height: 40px !important;
  }

  .action-dropdown {
    min-height: 44px;
    min-width: 44px;
    width: 44px;
    height: 44px;
  }

  .table tbody tr:hover {
    background-color: transparent; /* Disable hover on touch devices */
  }

  /* Disable hover effects on touch devices */
  .action-btn:hover {
    transform: none;
    box-shadow: none;
  }

  .action-btn:hover i {
    transform: none;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .stats-icon i {
    transform: scale(0.9); /* Slightly smaller icons on high DPI */
  }

  .badge {
    font-weight: 600; /* Bolder text on high DPI */
  }
}

/* ===== LOADING AND ANIMATIONS ===== */

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Loading Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.users-content {
  animation: fadeIn 0.5s ease-in-out;
}

.form-card {
  animation: fadeIn 0.3s ease-in-out;
}

/* Hover Effects */
.stats-card:hover .stats-icon {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

.stats-card:hover .stats-icon i {
  transform: rotate(5deg);
  transition: transform 0.3s ease;
}

/* Focus States */
.btn:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control:focus, .form-select:focus {
  transform: translateY(-1px);
}

/* Custom Scrollbar */
.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Utility Classes for Responsive Design */
.text-truncate-responsive {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .text-truncate-responsive {
    max-width: 120px;
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .text-truncate-responsive {
    max-width: 150px;
  }
}

@media (min-width: 993px) {
  .text-truncate-responsive {
    max-width: 200px;
  }
}

/* Responsive spacing utilities */
.spacing-responsive {
  padding: 1rem;
}

@media (max-width: 576px) {
  .spacing-responsive {
    padding: 0.5rem;
  }
}

@media (min-width: 992px) {
  .spacing-responsive {
    padding: 1.5rem;
  }
}

/* Responsive font sizes */
.fs-responsive {
  font-size: 1rem;
}

@media (max-width: 576px) {
  .fs-responsive {
    font-size: 0.875rem;
  }
}

@media (min-width: 1200px) {
  .fs-responsive {
    font-size: 1.125rem;
  }
}

/* Container improvements */
.container-fluid {
  max-width: 100%;
  padding-left: 15px;
  padding-right: 15px;
}

@media (min-width: 1400px) {
  .container-fluid {
    max-width: 1320px;
    margin: 0 auto;
  }
}

/* Improved table responsiveness */
.table-responsive {
  border-radius: 0.5rem;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
  .table-responsive {
    border-radius: 0.25rem;
  }
}

/* Enhanced button groups */
.btn-group-responsive {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

@media (max-width: 576px) {
  .btn-group-responsive {
    flex-direction: column;
    width: 100%;
  }

  .btn-group-responsive .btn {
    width: 100%;
  }
}

/* Print Styles */
@media print {
  .header-section,
  .form-card,
  .action-buttons,
  .btn,
  .dropdown {
    display: none !important;
  }

  .users-table-card {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .table {
    font-size: 12px !important;
  }

  .d-none {
    display: table-cell !important;
  }
}

/* ===== NG-SELECT CUSTOM STYLING ===== */

/* ng-select Custom Styling to match form controls */
.ng-select-custom {
  width: 100%;
}

.ng-select-custom.form-control-lg {
  font-size: 1rem;
}

.ng-select-custom.ng-select .ng-select-container {
  /* Match existing form-control and form-select styling */
  border: 2px solid #e9ecef !important;
  border-radius: 0.5rem !important;
  padding: 0 !important;
  background-color: #fff !important;
  transition: all 0.3s ease !important;
  box-shadow: none !important;
  min-height: 58px !important; /* Match form-control-lg height */
  font-size: 1rem !important;
}

/* Hover and focus states to match form controls */
.ng-select-custom.ng-select .ng-select-container:hover {
  border-color: #007bff !important;
}

.ng-select-custom.ng-select.ng-select-focused .ng-select-container {
  border-color: #007bff !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15) !important;
  background-color: #fff !important;
}

/* Validation states to match form controls */
.ng-select-custom.ng-select.ng-invalid.ng-touched .ng-select-container {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15) !important;
}

.ng-select-custom.ng-select.ng-valid.ng-touched .ng-select-container {
  border-color: #28a745 !important;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.15) !important;
}

/* Placeholder styling to match form controls */
.ng-select-custom.ng-select .ng-placeholder {
  color: #6c757d !important;
  font-size: 1rem !important;
  padding: 0 !important;
  line-height: 1.5 !important;
}

/* Value container styling to match form-control-lg */
.ng-select-custom.ng-select .ng-value-container {
  padding: 1rem 1.25rem !important; /* Match form-control-lg padding */
  font-size: 1rem !important;
  min-height: calc(100% - 4px) !important;
  align-items: center !important;
  flex-wrap: wrap !important;
}

.ng-select-custom.ng-select .ng-value {
  font-size: 1rem !important;
  color: #495057 !important;
  line-height: 1.5 !important;
}

/* Arrow and clear button positioning */
.ng-select-custom.ng-select .ng-arrow-wrapper {
  width: 25px !important;
  padding-right: 1.25rem !important; /* Match form-control-lg padding */
}

.ng-select-custom.ng-select .ng-clear-wrapper {
  width: 17px !important;
  padding-right: 0.5rem !important;
}

/* Multi-select tags styling - more subtle to match form design */
.ng-select-custom.ng-select.ng-select-multiple .ng-value-container .ng-value {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
  color: white !important;
  border-radius: 0.375rem !important;
  padding: 0.25rem 0.5rem !important;
  margin: 0.125rem 0.25rem 0.125rem 0 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3) !important;
  display: inline-flex !important;
  align-items: center !important;
  max-width: 100% !important;
  border: none !important;
}

.ng-select-custom.ng-select.ng-select-multiple .ng-value-container .ng-value .ng-value-label {
  color: white !important;
  font-weight: 500 !important;
  text-overflow: ellipsis !important;
  overflow: hidden !important;
  white-space: nowrap !important;
}

.ng-select-custom.ng-select.ng-select-multiple .ng-value-container .ng-value .ng-value-icon {
  color: rgba(255, 255, 255, 0.8) !important;
  transition: all 0.2s ease !important;
  margin-left: 0.5rem !important;
  cursor: pointer !important;
  border-radius: 50% !important;
  width: 16px !important;
  height: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.ng-select-custom.ng-select.ng-select-multiple .ng-value-container .ng-value .ng-value-icon:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.1) !important;
}

/* Dropdown styling */
.ng-dropdown-panel {
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: white;
  z-index: 1050;
  margin-top: 0.25rem;
}

.ng-dropdown-panel .ng-dropdown-panel-items {
  max-height: 300px;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f8f9fa;
  transition: all 0.2s ease;
  cursor: pointer;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
  background-color: #f8f9fa;
  transform: translateX(2px);
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  font-weight: 500;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-highlighted {
  background-color: #e3f2fd;
  color: #0056b3;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-disabled {
  color: #6c757d;
  background-color: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Search input styling */
.ng-select-custom .ng-input {
  padding: 0;
}

.ng-select-custom .ng-input input {
  font-size: inherit;
  color: #495057;
  border: none;
  outline: none;
  background: transparent;
  padding: 0.25rem 0;
  width: 100%;
}

.ng-select-custom .ng-input input::placeholder {
  color: #6c757d;
  opacity: 0.8;
}

/* Placeholder styling */
.ng-select-custom .ng-placeholder {
  color: #6c757d;
  font-size: inherit;
  padding: 0;
  line-height: 1.5;
}

/* Arrow and clear button styling */
.ng-select-custom .ng-arrow-wrapper {
  width: 25px;
  padding-right: 0.75rem;
}

.ng-select-custom .ng-arrow-wrapper .ng-arrow {
  border-color: #6c757d transparent transparent;
  border-style: solid;
  border-width: 5px 5px 0;
}

.ng-select-custom.ng-select-opened .ng-arrow-wrapper .ng-arrow {
  border-color: transparent transparent #6c757d;
  border-width: 0 5px 5px;
}

.ng-select-custom .ng-clear-wrapper {
  width: 17px;
  padding-right: 0.5rem;
}

.ng-select-custom .ng-clear-wrapper .ng-clear {
  color: #6c757d;
  font-size: 18px;
  line-height: 1;
}

.ng-select-custom .ng-clear-wrapper .ng-clear:hover {
  color: #dc3545;
}

/* Loading and not found templates */
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option[role="option"] {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 1rem;
}

/* Custom option icons */
.ng-dropdown-panel .ng-dropdown-panel-items .ng-option i {
  color: #007bff;
  margin-right: 0.5rem;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected i {
  color: white;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ng-select-custom {
    min-height: 44px;
    font-size: 1rem;
  }

  .ng-select-custom .ng-value-container {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }

  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
    padding: 0.625rem 0.75rem;
    font-size: 0.9rem;
  }

  .ng-select-custom.ng-select-multiple .ng-value-container .ng-value {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    margin: 0.1rem 0.15rem 0.1rem 0;
  }
}

/* Additional fixes for proper display */
.ng-select-custom .ng-select-container {
  display: flex;
  align-items: center;
}

.ng-select-custom .ng-value-container {
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  align-items: center;
}

.ng-select-custom.ng-select-single .ng-value-container {
  height: 100%;
}

.ng-select-custom.ng-select-multiple .ng-value-container {
  min-height: calc(100% - 4px);
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

/* Additional styling to ensure perfect form integration */
.ng-select-custom.ng-select {
  display: block !important;
  width: 100% !important;
  font-weight: 400 !important;
  line-height: 1.5 !important;
  color: #495057 !important;
  background-clip: padding-box !important;
}

/* Ensure ng-select behaves like form-control */
.ng-select-custom.ng-select .ng-select-container {
  display: flex !important;
  align-items: center !important;
  position: relative !important;
}

/* Override any conflicting ng-select default styles */
.ng-select-custom.ng-select.ng-select-single .ng-value-container .ng-value {
  color: #495057 !important;
  font-size: 1rem !important;
  padding: 0 !important;
  margin: 0 !important;
  background: transparent !important;
  border: none !important;
}

/* Ensure single select looks exactly like other form controls */
.ng-select-custom.ng-select.ng-select-single .ng-value-container {
  padding: 1rem 1.25rem !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
}

@media (max-width: 576px) {
  .ng-select-custom {
    min-height: 40px;
  }

  .ng-select-custom .ng-value-container {
    padding: 0.375rem 0.5rem;
    font-size: 0.85rem;
  }

  .ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
    padding: 0.5rem;
    font-size: 0.85rem;
  }
}

/* Component-specific ng-select styling is now handled in global styles.css */