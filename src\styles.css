/* You can add global styles to this file, and also import other style files */
@import 'bootstrap/dist/css/bootstrap.min.css';
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');
@import "@ng-select/ng-select/themes/default.theme.css";

body {
    color: #282828;
    background-color: #fff;
    font-family: "Roboto", sans-serif;
    font-weight: 400;
}

a {
    color: #000;
    text-decoration: none;
    transition: 0.3s;
}

a:hover {
    text-decoration: none;
}

.text-underline {
    text-decoration: underline;
}

img {
    max-width: 100%;
}

ol,
ul {
    list-style-image: none;
    list-style-position: outside;
    list-style: none;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1320px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #000;
    font-family: "Roboto", sans-serif;
    font-weight: 400;
}

h1 {
    font-size: 45px;
}

h2 {
    font-size: 38px;
}

h3 {
    font-size: 28px;
}

.logo {
    font-size: 60px;
    font-weight: bold;
    color: #0099E3;
}

.login-card {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    background-position: center;
    padding: 30px 12px;
}

.login-card h1 {
    font-weight: 500;
}

.login-card .login-main {
    width: 500px;
    padding: 20px 40px;
    border-radius: 20px;
    margin: 0 auto;
    background-color: #fff;
    border: solid 1px #D2D2D2;
}

.login-card .login-main .theme-form .form-group {
    margin-bottom: 10px;
    position: relative;
}

label {
    font-size: 14px;
}

input[type=text],
input[type=email],
input[type=search],
input[type=password],
input[type=number],
input[type=tel],
input[type=date],
input[type=datetime-local],
input[type=time],
input[type=datetime-local],
input[type=month],
input[type=week],
input[type=url],
input[type=file],
textarea,
select {
    border-color: #f3f3ff;
    background-color: #f3f3ff;
    font-size: 14px;
    color: #898989;
    padding: 12px 10px;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 12px;
}

input:hover,
input:focus,
.form-control:focus {
    border: 1px solid #e3e3f1;
    box-shadow: none !important;
    transition: all 0.3s ease;
    outline: none;
    background-color: #f3f3ff;
}

.btn {
    text-transform: uppercase;
    font-size: 22px;
    font-weight: 700;
    border-radius: 12px;
}

.btn-primary {
    background-color: #0099E3;
    border-color: #0099E3;
}

.btn-primary:hover {
    background-color: #017bb8;
    border-color: #017bb8;
}

.main-container {
    width: 80%;
    margin: 0 auto;
}

header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 50px;
    margin-top: 20px;
}

.company-logo {
    font-size: 40px;
    font-weight: 700;
    color: #2E6986;
}

.time {
    font-size: 35px;
    font-weight: 700;
    color: #000;
}

header .logo {
    font-size: 40px;
    margin-left: 15px;
}

.topmenu .dropdown-toggle::after {
    display: none;
}

.topmenu img {
    width: 80%;
}

.employee-search-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 50px;
}

.employee-search-list a {
    border: solid 1px #D2D2D2;
    padding: 10px 15px;
    border-radius: 10px;
    margin: 15px 20px;
    min-width: 73px;
    text-align: center;
    font-size: 35px;
}

.graybox {
    font-size: 32px;
    border: solid 1px #D2D2D2;
    border-radius: 20px;
    margin-bottom: 20px;
    background-color: #F5F5F7;
}

.graybox a {
    padding: 10px 25px;
    display: block;
}

.question-box {
    border: solid 1px #D2D2D2;
    border-radius: 20px;
    padding: 20px 30px 0 30px;
    margin-bottom: 20px;
}

.question-box h3 {
    font-size: 30px;
    font-weight: 700;
    text-align: left;
}

.question-box .graybox {
    font-size: 25px;
}

.question-box .graybox a {
    padding: 6px 25px;
    display: inline-block;
}

.greatjob-box {
    background-color: #0099E3;
    border-radius: 20px;
    padding: 10px 20px;
    color: #fff;
    display: inline-flex;
    align-items: center;
    margin: 20px auto;
}

.greatjob-box h3 {
    margin-bottom: 0;
    color: #fff;
    font-weight: bold;
}

.greatjob-box img {
    height: 60px;
    margin-right: 10px;
}

.profile-wrapper {
    max-width: 600px;
    margin: 0 auto;
}

.table-default {
    border: solid 1px #EEEAF5;
    font-family: "Roboto", sans-serif;
    font-size: 20px;
}

.table-default thead th {
    background-color: #363848;
    color: #fff;
    text-align: left;
}

.table-default tbody td {
    text-align: left;
}

.table-default tbody td,
.table-default thead th {
    padding: 8px 12px;
}

.table-default.table-striped>tbody>tr:nth-of-type(odd)>* {
    background-color: #ffffff;
    --bs-table-bg-type: #fff
}

.table-default.table-striped>tbody>tr:nth-of-type(even)>* {
    background-color: #F5F5F7;
}

/* Tablet Optimizations */
@media (min-width: 768px) and (max-width: 1024px) {
    .main-container {
        width: 90%;
    }

    h1 {
        font-size: 40px;
    }

    h3 {
        font-size: 24px;
    }

    .employee-search-list a {
        font-size: 30px;
        min-width: 65px;
        padding: 8px 12px;
        margin: 12px 15px;
    }

    .graybox {
        font-size: 28px;
    }

    .question-box .graybox {
        font-size: 22px;
    }

    .btn {
        font-size: 18px;
        padding: 12px 24px;
    }

    .company-logo {
        font-size: 36px;
    }

    .time {
        font-size: 30px;
    }

    header .logo {
        font-size: 36px;
    }
}

/* Touch-friendly improvements */
.graybox .btn {
    min-height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.graybox a {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Landscape orientation optimizations */
@media (orientation: landscape) and (max-height: 768px) {
    header {
        margin-bottom: 30px;
        margin-top: 15px;
    }

    .login-card {
        padding: 20px 12px;
    }

    .question-box {
        padding: 15px 25px 0 25px;
        margin-bottom: 15px;
    }
}

/* Global ng-select custom styling */
.ng-select.custom {
    border: 2px solid #e9ecef !important;
    min-height: 58px !important;
    border-radius: 0.5rem !important;
    background-color: #fff !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
}

.ng-select.custom .ng-select-container {
    min-height: 58px !important;
    border: none !important;
    border-radius: 0.5rem !important;
    background-color: transparent !important;
    padding: 0 !important;
}

.ng-select.custom .ng-value-container {
    padding: 0.75rem 1.25rem !important;
    font-size: 1rem !important;
    color: #495057 !important;
    min-height: calc(100% - 4px) !important;
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    gap: 0.25rem !important;
}

.ng-select.custom .ng-placeholder {
    color: #6c757d !important;
    font-size: 1rem !important;
    top: 0px !important;
    position: relative !important;
}

/* 
.ng-select.custom .ng-arrow-wrapper {
    width: 25px !important;
    padding-right: 1.25rem !important;
} */

.ng-select.custom .ng-clear-wrapper {
    width: 17px !important;
    padding-right: 0.5rem !important;
}

/* Hover and focus states */
.ng-select.custom:hover {
    border-color: #007bff !important;
}

.ng-select.custom.ng-select-focused {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15) !important;
}

/* Validation states */
.ng-select.custom.ng-invalid.ng-touched {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.15) !important;
}

/* Multi-select tags */
.ng-select.custom.ng-select-multiple .ng-value {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: white !important;
    border-radius: 0.375rem !important;
    padding: 0.375rem 0.75rem !important;
    margin: 0.125rem 0.25rem 0.125rem 0 !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    border: none !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    max-width: 100% !important;
    flex-direction: row-reverse;
}

.ng-select.custom.ng-select-multiple .ng-value .ng-value-label {
    /* color: white !important; */
    font-weight: 500 !important;
    text-overflow: ellipsis !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    flex: 1 !important;
}

.ng-select.custom.ng-select-multiple .ng-value .ng-value-icon {
    color: rgba(255, 255, 255, 0.8) !important;
    cursor: pointer !important;
    width: 18px !important;
    height: 18px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 22px !important;
    transition: all 0.2s ease !important;
    border-left: 1px solid rgb(183.6, 218.88, 255) !important;
    border-right: none !important;
    padding-left: 15px !important;

}

.ng-select.custom.ng-select-multiple .ng-value .ng-value-icon:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1) !important;
}

/* .ng-select.custom.ng-select-multiple .ng-value .ng-value-icon:before {
    content: '×' !important;
    font-size: 16px !important;
    font-weight: bold !important;
} */

/* Dropdown panel styling */
.ng-dropdown-panel {
    border: 1px solid #e9ecef !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    background: white !important;
    z-index: 1050 !important;
    margin-top: 0.25rem !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items {
    max-height: 300px !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
    padding: 0.75rem 1rem !important;
    border-bottom: 1px solid #f8f9fa !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
    background-color: #f8f9fa !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: white !important;
    font-weight: 500 !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%) !important;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-highlighted {
    background-color: #e3f2fd !important;
    color: #0056b3 !important;
}

/* Single select specific styling */
.ng-select.custom.ng-select-single .ng-value-container {
    padding: 1rem 1.25rem !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
}

.ng-select.custom.ng-select-single .ng-value {
    color: #495057 !important;
    font-size: 1rem !important;
    padding: 0 !important;
    margin: 0 !important;
    background: transparent !important;
    border: none !important;
    line-height: 1.5 !important;
}

/* Search input in dropdown */
.ng-select.custom .ng-input input {
    font-size: 1rem !important;
    color: #495057 !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
    padding: 0.25rem 0 !important;
    width: 100% !important;
}

.ng-select.custom .ng-input input::placeholder {
    color: #6c757d !important;
    opacity: 0.8 !important;
}

/* Responsive adjustments for mobile */
/* @media (max-width: 768px) {
    .ng-select.custom.ng-select-multiple .ng-value {
        padding: 0.25rem 0.5rem !important;
        font-size: 0.8rem !important;
        margin: 0.1rem 0.15rem 0.1rem 0 !important;
        gap: 0.375rem !important;
    }

    .ng-select.custom.ng-select-multiple .ng-value .ng-value-icon {
        width: 16px !important;
        height: 16px !important;
        font-size: 12px !important;
    }

    .ng-select.custom .ng-value-container {
        padding: 0.5rem 1rem !important;
        gap: 0.125rem !important;
    }
}

@media (max-width: 576px) {
    .ng-select.custom.ng-select-multiple .ng-value {
        padding: 0.2rem 0.4rem !important;
        font-size: 0.75rem !important;
        margin: 0.05rem 0.1rem 0.05rem 0 !important;
        gap: 0.25rem !important;
    }

    .ng-select.custom.ng-select-multiple .ng-value .ng-value-icon {
        width: 14px !important;
        height: 14px !important;
        font-size: 10px !important;
    }
} */