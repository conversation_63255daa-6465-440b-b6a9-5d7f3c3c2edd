import { NgModule, Optional, SkipSelf } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS } from '@angular/common/http';

// Services
import { AuthService } from './services/auth.service';
import { EmployeeService } from './services/employee.service';
import { TimeService } from './services/time.service';
import { DepartmentService } from './services/department.service';
import { UserManagementService } from './services/user-management.service';
import { TranslationService } from './services/translation.service';
import { DataRefreshService } from './services/data-refresh.service';

// Guards
import { AuthGuard } from './guards/auth.guard';
import { AdminGuard } from './guards/admin.guard';
import { EmployeeGuard } from './guards/employee.guard';
import { RoleGuard } from './guards/role.guard';
import { CompanySuperAdminGuard } from './guards/company-super-admin.guard';

// Interceptors
import { AuthInterceptor } from './interceptors/auth.interceptor';
import { LanguageInterceptor } from './interceptors/language.interceptor';

@NgModule({
  declarations: [],
  imports: [
    CommonModule
  ],
  providers: [
    // Services
    AuthService,
    EmployeeService,
    TimeService,
    DepartmentService,
    UserManagementService,
    TranslationService,
    DataRefreshService,
    
    // Guards
    AuthGuard,
    AdminGuard,
    EmployeeGuard,
    RoleGuard,
    CompanySuperAdminGuard,
    
    // Interceptors
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: LanguageInterceptor,
      multi: true
    }
  ]
})
export class CoreModule {
  constructor(@Optional() @SkipSelf() parentModule: CoreModule) {
    if (parentModule) {
      throw new Error('CoreModule is already loaded. Import it in the AppModule only.');
    }
  }
}
