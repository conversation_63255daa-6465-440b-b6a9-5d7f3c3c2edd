import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslatePipe } from '../../../shared/pipes/translate.pipe';

@Component({
  selector: 'app-employee-search',
  standalone: true,
  imports: [CommonModule, TranslatePipe],
  templateUrl: './employee-search.component.html',
  styleUrls: ['./employee-search.component.css']
})
export class EmployeeSearchComponent {
  
  alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

  constructor(private router: Router) {}

  onLetterClick(letter: string): void {
    this.router.navigate(['/employee/list'], { queryParams: { letter } });
  }
}
