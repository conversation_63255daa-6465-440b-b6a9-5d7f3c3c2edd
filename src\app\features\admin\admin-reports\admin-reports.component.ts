import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ReportsService, SurveyReportsResponse, ReportFilters, DashboardFilters } from '../../../core/services/reports.service';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';
import { ChartComponent } from '../../../shared/components/chart/chart.component';
import { TranslatePipe } from '../../../shared/pipes/translate.pipe';
import { DataRefreshService } from '../../../core/services/data-refresh.service';
import { RefreshableComponentBase } from '../../../shared/components/base/refreshable-component.base';

@Component({
  selector: 'app-admin-reports',
  standalone: true,
  imports: [CommonModule, FormsModule, LoadingComponent, ChartComponent, TranslatePipe],
  templateUrl: './admin-reports.component.html',
  styleUrls: ['./admin-reports.component.css']
})
export class AdminReportsComponent extends RefreshableComponentBase implements OnInit {
  loading = false;
  reportData: SurveyReportsResponse | null = null;

  // Filter properties
  filters: ReportFilters = {};
  dashboardFilters: DashboardFilters = {};
  selectedDepartment = '';
  selectedDateRange = '30d';
  startDate = '';
  endDate = '';
  selectedPeriod: 'daily' | 'weekly' | 'monthly' | 'yearly' = 'monthly';

  // Chart data properties
  categoryChartData: any = null;
  departmentResponseRateData: any = null;
  departmentSatisfactionData: any = null;
  trendChartData: any = null;

  constructor(
    private reportsService: ReportsService,
    private router: Router,
    dataRefreshService: DataRefreshService
  ) {
    super(dataRefreshService, 'AdminReportsComponent');
  }

  override ngOnInit(): void {
    super.ngOnInit(); // Register for language change refresh
    this.initializeDateFilters();
    this.loadReportData();
  }

  // Implement the abstract refreshData method
  refreshData(): void {
    console.log('🔄 AdminReportsComponent: Refreshing data due to language change');
    this.loadReportData();
  }

  initializeDateFilters(): void {
    // Set default date range to last 30 days
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 30);

    this.startDate = startDate.toISOString().split('T')[0];
    this.endDate = endDate.toISOString().split('T')[0];
  }

  loadReportData(): void {
    this.loading = true;

    // Prepare dashboard filters
    this.dashboardFilters = {
      startDate: this.startDate,
      endDate: this.endDate,
      departmentId: this.selectedDepartment || undefined,
      period: this.selectedPeriod
    };

    // Use the new dashboard API
    this.reportsService.getDashboardReports(this.dashboardFilters).subscribe({
      next: (data) => {
        this.reportData = data;
        this.setupChartData();
        this.loading = false;
        console.log('Dashboard data loaded successfully:', data);
      },
      error: (error) => {
        console.error('Error loading dashboard data:', error);
        this.loading = false;
      }
    });
  }

  setupChartData(): void {
    if (!this.reportData) return;
    

    // Category Distribution Pie Chart
    this.categoryChartData = this.reportData.categoryDistribution.chartData;

    // Department Response Rate Bar Chart
    this.departmentResponseRateData = this.reportData.departmentAnalytics.responseRateChart;

    // Department Satisfaction Bar Chart
    this.departmentSatisfactionData = this.reportData.departmentAnalytics.satisfactionChart;

    // Trend Line Chart
    // this.trendChartData = this.reportData.trendAnalysis.chartData;
  }

  onFilterChange(): void {
    // Update both old and new filter formats for compatibility
    this.filters = {
      department: this.selectedDepartment || undefined,
      dateRange: this.selectedDateRange || undefined
    };

    this.dashboardFilters = {
      startDate: this.startDate,
      endDate: this.endDate,
      departmentId: this.selectedDepartment || undefined,
      period: this.selectedPeriod
    };

    this.loadReportData();
  }

  onDateRangeChange(): void {
    this.onFilterChange();
  }

  onPeriodChange(): void {
    this.onFilterChange();
  }

  goBack(): void {
    this.router.navigate(['/admin']);
  }

  exportReport(format: 'pdf' | 'excel' = 'pdf'): void {
    if (!this.reportData?.userAccess.canExport) {
      alert('You do not have permission to export reports');
      return;
    }

    this.reportsService.exportReport(format, this.filters).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `survey-report-${new Date().toISOString().split('T')[0]}.${format === 'pdf' ? 'pdf' : 'xlsx'}`;
        link.click();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error exporting report:', error);
        alert('Failed to export report. Please try again.');
      }
    });
  }

  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  formatPercentage(value: number): string {
    return `${value.toFixed(1)}%`;
  }

  /**
   * Set predefined date ranges for quick selection
   */
  setDateRange(days: number): void {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - days);

    this.startDate = startDate.toISOString().split('T')[0];
    this.endDate = endDate.toISOString().split('T')[0];

    this.onDateRangeChange();
  }

  /**
   * Quick date range buttons
   */
  setLast7Days(): void {
    this.setDateRange(7);
  }

  setLast30Days(): void {
    this.setDateRange(30);
  }

  setLast90Days(): void {
    this.setDateRange(90);
  }

  /**
   * Get current filter summary for display
   */
  getFilterSummary(): string {
    const parts = [];

    if (this.startDate && this.endDate) {
      parts.push(`${this.startDate} to ${this.endDate}`);
    }

    if (this.selectedDepartment) {
      const dept = this.reportData?.availableFilters.departments.find(d => d.value === this.selectedDepartment);
      parts.push(`Department: ${dept?.label || this.selectedDepartment}`);
    }

    parts.push(`Period: ${this.selectedPeriod}`);

    return parts.join(' | ');
  }

  /**
   * Check if API is available (for debugging)
   */
  testApiConnection(): void {
    console.log('Testing API connection with current filters:', this.dashboardFilters);
    this.loadReportData();
  }
}
