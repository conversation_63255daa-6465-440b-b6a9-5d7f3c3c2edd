import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { AuthService, LoginRequest } from '../../../core/services/auth.service';
import { LoadingComponent } from '../../../shared/components/loading/loading.component';

@Component({
  selector: 'app-employee-login',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule, LoadingComponent],
  templateUrl: './employee-login.component.html',
  styleUrls: ['./employee-login.component.css']
})
export class EmployeeLoginComponent implements OnInit {
  loginData: LoginRequest = {
    email: '',
    password: ''
  };

  loading = false;
  errorMessage = '';
  showPassword = false;

  constructor(
    private authService: AuthService,
    private router: Router
  ) { }

  ngOnInit(): void {
    // Check if user is already logged in
    if (this.authService.isAuthenticated()) {
      // If already logged in as employee, redirect to employee dashboard
      if (this.authService.isEmployee()) {
        console.log('User already logged in as employee, redirecting to employee dashboard');
        this.router.navigate(['/employee']);
      } else {
        // If logged in as admin, redirect to admin dashboard
        console.log('User already logged in as admin, redirecting to admin dashboard');
        this.router.navigate(['/admin']);
      }
    }
  }

  onSubmit(): void {
    if (!this.loginData.email || !this.loginData.password) {
      this.errorMessage = 'Please enter both email and password';
      return;
    }

    this.loading = true;
    this.errorMessage = '';

    console.log('Employee Login Attempt:', {
      email: this.loginData.email,
      infiniteToken: true // Pass infinite token flag
    });

    // Use the employee-specific login method
    this.authService.employeeLogin(this.loginData).subscribe({
      next: (response) => {
        console.log('Employee Login Successful:', response);

        // Always redirect to employee dashboard after successful login
        // Role-based access control will be handled by route guards
        console.log('Redirecting to employee dashboard...');
        this.router.navigate(['/employee']);
        this.loading = false;
      },
      error: (error) => {
        console.error('Employee Login Error:', error);
        this.loading = false;

        if (error.status === 401) {
          this.errorMessage = 'Invalid email or password';
        } else if (error.status === 403) {
          this.errorMessage = 'Access denied. Employee access required.';
        } else if (error.error && error.error.message) {
          this.errorMessage = error.error.message;
        } else {
          this.errorMessage = 'Login failed. Please try again.';
        }
      }
    });
  }

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  goToAdminLogin(): void {
    this.router.navigate(['/login']);
  }

  // Demo login for testing
  demoEmployeeLogin(): void {
    this.loginData = {
      email: '<EMAIL>',
      password: 'password123'
    };
    this.onSubmit();
  }

  clearForm(): void {
    this.loginData = {
      email: '',
      password: ''
    };
    this.errorMessage = '';
  }
}
