import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-role-debug',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './role-debug.component.html',
  styleUrls: ['./role-debug.component.css']
})
export class RoleDebugComponent implements OnInit {
  currentUser: any = null;
  isAuthenticated = false;
  isEmployee = false;
  userRole = '';

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.updateUserInfo();
  }

  updateUserInfo(): void {
    this.currentUser = this.authService.getCurrentUser();
    this.isAuthenticated = this.authService.isAuthenticated();
    this.isEmployee = this.authService.isEmployee();
    this.userRole = this.authService.getUserRoleString(this.currentUser) || 'No Role';
  }

  testEmployeeRoute(): void {
    console.log('Testing employee route access...');
    this.router.navigate(['/employee']);
  }

  testAdminRoute(): void {
    console.log('Testing admin route access...');
    this.router.navigate(['/admin']);
  }

  testDepartmentRoute(): void {
    console.log('Testing department route access...');
    this.router.navigate(['/admin/departments']);
  }

  testUserManagementRoute(): void {
    console.log('Testing user management route access...');
    this.router.navigate(['/admin/users']);
  }

  logout(): void {
    this.authService.logout();
    this.updateUserInfo();
    this.router.navigate(['/']);
  }
}
