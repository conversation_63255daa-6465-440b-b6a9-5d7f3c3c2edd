import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
// import { RoleDebugComponent } from '../../shared/components/role-debug/role-debug.component';

@Component({
  selector: 'app-login-selector',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './login-selector.component.html',
  styleUrls: ['./login-selector.component.css']
})
export class LoginSelectorComponent {
  
  constructor() {}

}
