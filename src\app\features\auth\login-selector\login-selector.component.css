/* Login Selector Container */
.login-selector-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
}

/* Selector Card */
.selector-card {
  background: white;
  border-radius: 1.5rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  max-width: 900px;
  width: 100%;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Selector Header */
.selector-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 3rem 2rem 2rem;
  text-align: center;
  border-bottom: 4px solid #007bff;
}

.logo-text {
  font-size: 4rem;
  font-weight: 900;
  color: #007bff;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.selector-header h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.selector-header p {
  font-size: 1.1rem;
  margin-bottom: 0;
}

/* Login Options */
.login-options {
  padding: 3rem 2rem;
}

/* Login Option Cards */
.login-option-card {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 1rem;
  padding: 2rem;
  text-align: center;
  transition: all 0.4s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.login-option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, transparent, #007bff, transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.login-option-card:hover::before {
  transform: translateX(100%);
}

.login-option-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.employee-card:hover {
  border-color: #007bff;
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
}

.admin-card:hover {
  border-color: #ffc107;
  background: linear-gradient(135deg, #fffbf0 0%, #fff3cd 100%);
}

/* Card Icon */
.card-icon {
  margin-bottom: 1.5rem;
  transition: transform 0.3s ease;
}

.login-option-card:hover .card-icon {
  transform: scale(1.1);
}

/* Card Content */
.card-content {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  color: #2c3e50;
  font-weight: 700;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.card-description {
  color: #6c757d;
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.6;
}

/* Feature List */
.feature-list {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
  text-align: left;
}

.feature-list li {
  padding: 0.5rem 0;
  color: #495057;
  font-weight: 500;
}

/* Buttons */
.btn {
  border-radius: 0.75rem;
  font-weight: 600;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  text-transform: none;
  margin-top: auto;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  box-shadow: 0 8px 25px rgba(0, 123, 255, 0.4);
}

.btn-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  border: none;
  color: #212529;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-warning:hover {
  background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
  color: #212529;
}

/* Selector Footer */
.selector-footer {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 1.5rem 2rem;
  border-top: 1px solid #dee2e6;
}

.selector-footer p {
  font-size: 0.95rem;
  color: #6c757d;
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-selector-container {
    padding: 1rem 0.5rem;
  }
  
  .selector-card {
    margin: 0;
    border-radius: 1rem;
  }
  
  .selector-header {
    padding: 2rem 1.5rem 1.5rem;
  }
  
  .logo-text {
    font-size: 3rem;
  }
  
  .selector-header h2 {
    font-size: 1.5rem;
  }
  
  .login-options {
    padding: 2rem 1.5rem;
  }
  
  .login-option-card {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .card-title {
    font-size: 1.3rem;
  }
  
  .btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
  
  .selector-footer {
    padding: 1rem 1.5rem;
  }
}

@media (max-width: 576px) {
  .card-icon i {
    font-size: 3rem !important;
  }
  
  .feature-list {
    margin-bottom: 1.5rem;
  }
  
  .feature-list li {
    padding: 0.25rem 0;
    font-size: 0.9rem;
  }
}

/* Accessibility */
.btn:focus,
.login-option-card:focus {
  outline: 3px solid #007bff;
  outline-offset: 2px;
}

/* Animation delays for staggered effect */
.employee-card {
  animation-delay: 0.2s;
}

.admin-card {
  animation-delay: 0.4s;
}
