import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';
import { TranslationService } from '../services/translation.service';

@Injectable()
export class LanguageInterceptor implements HttpInterceptor {

  constructor(private translationService: TranslationService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Get current language code
    const currentLanguage = this.translationService.getCurrentLanguage();
    
    // Clone the request and add language headers
    const languageRequest = req.clone({
      setHeaders: {
        'Accept-Language': currentLanguage,
        'X-Language': currentLanguage,
        'Content-Language': currentLanguage
      }
    });

    console.log(`🌐 Language Interceptor: Adding language headers - ${currentLanguage}`, {
      url: req.url,
      headers: {
        'Accept-Language': currentLanguage,
        'X-Language': currentLanguage,
        'Content-Language': currentLanguage
      }
    });

    return next.handle(languageRequest);
  }
}
