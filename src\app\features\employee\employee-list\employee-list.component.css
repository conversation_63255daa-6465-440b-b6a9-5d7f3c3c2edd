/* Employee list styles are already in global styles.css */

.graybox:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* Enhanced Employee Item Styles */
.employee-item {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 0.75rem;
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.employee-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #007bff;
}

.employee-item.survey-completed {
  border-left: 4px solid #28a745;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.employee-item.survey-completed:hover {
  border-color: #28a745;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
}

.employee-item.survey-pending {
  border-left: 4px solid #ffc107;
  background: linear-gradient(135deg, #fffbf0 0%, #fff3cd 100%);
}

.employee-item.survey-pending:hover {
  border-color: #ffc107;
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.employee-info {
  flex-grow: 1;
}

.employee-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.employee-details {
  font-size: 0.9rem;
}

.survey-status {
  flex-shrink: 0;
  margin-left: 1rem;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.85rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.status-badge.completed {
  background: #28a745;
  color: white;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.status-badge.pending {
  background: #ffc107;
  color: #212529;
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

.status-badge i {
  font-size: 0.9rem;
}

/* Empty State */
.empty-state {
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 1rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .employee-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 0.75rem;
  }

  .survey-status {
    margin-left: 0;
    margin-top: 0.75rem;
    align-self: stretch;
  }

  .status-badge {
    justify-content: center;
    padding: 0.5rem;
  }

  .employee-name {
    font-size: 1rem;
  }

  .employee-details {
    font-size: 0.8rem;
  }
}

@media (max-width: 576px) {
  .employee-item {
    margin-bottom: 0.75rem;
  }

  .employee-details small {
    display: block;
    margin-bottom: 0.25rem;
  }

  .employee-details .ms-3 {
    margin-left: 0 !important;
  }
}

/* Employee Cards */
.employee-card {
  border: 1px solid #e9ecef;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.employee-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.employee-avatar {
  flex-shrink: 0;
}

.card-title {
  color: #2c3e50;
  font-weight: 600;
}

.card-text {
  font-size: 0.875rem;
}

.card-text i {
  width: 16px;
  text-align: center;
}

/* Filter Card */
.card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
}

.form-label {
  color: #495057;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.form-select, .form-control {
  border: 1.5px solid #e9ecef;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

/* Empty State */
.empty-state {
  padding: 3rem 2rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 0.75rem;
  margin: 2rem 0;
}

/* API Test Alert */
.alert-info {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #b3e5fc;
  border-radius: 0.5rem;
}

/* Button Enhancements */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-outline-info:hover {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

/* Results Summary */
.alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #c3e6cb;
  border-left: 4px solid #28a745;
}

/* Responsive Design */
@media (max-width: 768px) {
  .employee-card .card-body {
    padding: 1rem;
  }

  .employee-avatar {
    margin-right: 0.75rem !important;
  }

  .card-title {
    font-size: 1rem;
  }

  .card-text {
    font-size: 0.8rem;
  }

  .empty-state {
    padding: 2rem 1rem;
  }
}
