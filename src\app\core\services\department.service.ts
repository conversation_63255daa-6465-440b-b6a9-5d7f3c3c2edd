import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

export interface Department {
  id?: number;
  name: string;
  description: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateDepartmentRequest {
  name: string;
  description: string;
}

export interface UpdateDepartmentRequest {
  name?: string;
  description?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private readonly API_URL = 'http://localhost:3000/api';

  constructor(private http: HttpClient) {}

  // Get all departments
  getAllDepartments(): Observable<Department[]> {
    return this.http.get<Department[]>(`${this.API_URL}/departments`)
      .pipe(catchError(this.handleError));
  }

  // Get department by ID
  getDepartmentById(id: number): Observable<Department> {
    return this.http.get<Department>(`${this.API_URL}/departments/${id}`)
      .pipe(catchError(this.handleError));
  }

  // Create new department
  createDepartment(department: CreateDepartmentRequest): Observable<Department> {
    return this.http.post<Department>(`${this.API_URL}/departments`, department)
      .pipe(catchError(this.handleError));
  }

  // Update department
  updateDepartment(id: number, department: UpdateDepartmentRequest): Observable<Department> {
    return this.http.put<Department>(`${this.API_URL}/departments/${id}`, department)
      .pipe(catchError(this.handleError));
  }

  // Delete department
  deleteDepartment(id: number): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/departments/${id}`)
      .pipe(catchError(this.handleError));
  }

  private handleError = (error: HttpErrorResponse) => {
    let errorMessage = 'An error occurred while processing the request';
    
    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.status === 400) {
      errorMessage = 'Invalid request data';
    } else if (error.status === 401) {
      errorMessage = 'Unauthorized access';
    } else if (error.status === 403) {
      errorMessage = 'Access forbidden';
    } else if (error.status === 404) {
      errorMessage = 'Department not found';
    } else if (error.status === 409) {
      errorMessage = 'Department already exists';
    } else if (error.status === 0) {
      errorMessage = 'Unable to connect to server. Please check your connection.';
    }
    
    return throwError(() => new Error(errorMessage));
  }
}
